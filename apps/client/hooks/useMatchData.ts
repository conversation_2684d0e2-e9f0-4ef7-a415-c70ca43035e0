"use client";

import { useState, useEffect, useCallback } from "react";
import { apiClient } from "@/utils/api";

// 通用的分页参数类型
export interface PaginationParams {
  page: number;
  pageSize: number;
}

// 通用的筛选参数类型
export interface MatchFilters {
  searchQuery?: string;
  category?: string;
  status?: string;
  grade?: string;
  subjects?: string[];
  location?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// 通用的API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  totalPages: number;
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

// 匹配请求数据hook
export function useMatchRequests(
  initialFilters: MatchFilters = {},
  initialPagination: PaginationParams = { page: 1, pageSize: 10 }
) {
  const [requests, setRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState(initialPagination);
  const [filters, setFilters] = useState(initialFilters);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const fetchRequests = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
      });

      // 添加筛选参数
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const result = await apiClient.request(`/api/match/requests?${params}`);
      
      if (result.success) {
        setRequests(result.data.items || []);
        setTotalPages(result.data.totalPages || 1);
        setTotalCount(result.data.totalCount || 0);
      } else {
        setError(result.message || "获取匹配请求失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("获取匹配请求失败:", err);
    } finally {
      setLoading(false);
    }
  }, [pagination, filters]);

  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  const updateFilters = useCallback((newFilters: Partial<MatchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
  }, []);

  const updatePagination = useCallback((newPagination: Partial<PaginationParams>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  const refresh = useCallback(() => {
    fetchRequests();
  }, [fetchRequests]);

  return {
    requests,
    loading,
    error,
    pagination,
    filters,
    totalPages,
    totalCount,
    updateFilters,
    updatePagination,
    refresh,
  };
}

// 规划师数据hook
export function useMatchPlanners(
  initialFilters: MatchFilters = {},
  initialPagination: PaginationParams = { page: 1, pageSize: 10 }
) {
  const [planners, setPlanners] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState(initialPagination);
  const [filters, setFilters] = useState(initialFilters);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const fetchPlanners = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
      });

      // 添加筛选参数
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v));
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const result = await apiClient.request(`/api/match/planners?${params}`);
      
      if (result.success) {
        setPlanners(result.data.items || []);
        setTotalPages(result.data.totalPages || 1);
        setTotalCount(result.data.totalCount || 0);
      } else {
        setError(result.message || "获取规划师列表失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("获取规划师列表失败:", err);
    } finally {
      setLoading(false);
    }
  }, [pagination, filters]);

  useEffect(() => {
    fetchPlanners();
  }, [fetchPlanners]);

  const updateFilters = useCallback((newFilters: Partial<MatchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // 重置到第一页
  }, []);

  const updatePagination = useCallback((newPagination: Partial<PaginationParams>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  const refresh = useCallback(() => {
    fetchPlanners();
  }, [fetchPlanners]);

  return {
    planners,
    loading,
    error,
    pagination,
    filters,
    totalPages,
    totalCount,
    updateFilters,
    updatePagination,
    refresh,
  };
}

// 匹配统计数据hook
export function useMatchStatistics(tenantId?: string, timeRange: string = "30d") {
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatistics = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({ timeRange });
      if (tenantId) {
        params.append("tenantId", tenantId);
      }

      const result = await apiClient.request(`/api/match/statistics?${params}`);
      
      if (result.success) {
        setStatistics(result.data);
      } else {
        setError(result.message || "获取统计数据失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("获取统计数据失败:", err);
    } finally {
      setLoading(false);
    }
  }, [tenantId, timeRange]);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  const refresh = useCallback(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    error,
    refresh,
  };
}

// 单个匹配请求详情hook
export function useMatchRequest(requestId: string) {
  const [request, setRequest] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRequest = useCallback(async () => {
    if (!requestId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiClient.request(`/api/match/requests/${requestId}`);
      
      if (result.success) {
        setRequest(result.data);
      } else {
        setError(result.message || "获取请求详情失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("获取请求详情失败:", err);
    } finally {
      setLoading(false);
    }
  }, [requestId]);

  useEffect(() => {
    fetchRequest();
  }, [fetchRequest]);

  const refresh = useCallback(() => {
    fetchRequest();
  }, [fetchRequest]);

  return {
    request,
    loading,
    error,
    refresh,
  };
}

// 匹配推荐hook
export function useMatchRecommendations(requestId: string) {
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRecommendations = useCallback(async () => {
    if (!requestId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await apiClient.request(`/api/match/recommendations/${requestId}`);

      if (result.success) {
        setRecommendations(result.data || []);
      } else {
        setError(result.message || "获取推荐失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("获取推荐失败:", err);
    } finally {
      setLoading(false);
    }
  }, [requestId]);

  useEffect(() => {
    fetchRecommendations();
  }, [fetchRecommendations]);

  const refresh = useCallback(() => {
    fetchRecommendations();
  }, [fetchRecommendations]);

  return {
    recommendations,
    loading,
    error,
    refresh,
  };
}

// 匹配操作hook（接受、拒绝、取消等）
export function useMatchActions() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const acceptMatch = useCallback(async (requestId: string, plannerId: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiClient.request(`/api/match/requests/${requestId}/accept`, {
        method: "POST",
        body: JSON.stringify({ plannerId }),
      });

      if (!result.success) {
        setError(result.message || "接受匹配失败");
        return false;
      }

      return true;
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("接受匹配失败:", err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const rejectMatch = useCallback(async (requestId: string, reason?: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiClient.request(`/api/match/requests/${requestId}/reject`, {
        method: "POST",
        body: JSON.stringify({ reason }),
      });

      if (!result.success) {
        setError(result.message || "拒绝匹配失败");
        return false;
      }

      return true;
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("拒绝匹配失败:", err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const cancelRequest = useCallback(async (requestId: string, reason?: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiClient.request(`/api/match/requests/${requestId}/cancel`, {
        method: "POST",
        body: JSON.stringify({ reason }),
      });

      if (!result.success) {
        setError(result.message || "取消请求失败");
        return false;
      }

      return true;
    } catch (err) {
      setError("网络错误，请稍后重试");
      console.error("取消请求失败:", err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    acceptMatch,
    rejectMatch,
    cancelRequest,
  };
}
