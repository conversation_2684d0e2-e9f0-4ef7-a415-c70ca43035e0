// 匹配模块的统一类型定义

// ========== 基础枚举类型 ==========

// 匹配请求状态
export enum MatchRequestStatus {
  PENDING = "PENDING",
  MATCHING = "MATCHING", 
  MATCHED = "MATCHED",
  CONFIRMED = "CONFIRMED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
}

// 需求分类
export enum RequirementCategory {
  LEARNING_ABILITY = "LEARNING_ABILITY",
  PSYCHOLOGICAL_SUPPORT = "PSYCHOLOGICAL_SUPPORT",
  COLLEGE_APPLICATION = "COLLEGE_APPLICATION",
  SPECIAL_ADMISSION = "SPECIAL_ADMISSION",
  HONGKONG_MACAO = "HONGKONG_MACAO",
  ENGLISH_COUNTRIES = "ENGLISH_COUNTRIES",
  NON_ENGLISH_COUNTRIES = "NON_ENGLISH_COUNTRIES",
  ACADEMIC_PLANNING = "ACADEMIC_PLANNING",
  CAREER_PLANNING = "CAREER_PLANNING",
  STUDY_ABROAD = "STUDY_ABROAD",
  EXAM_PREPARATION = "EXAM_PREPARATION",
  EXTRACURRICULAR = "EXTRACURRICULAR",
}

// 匹配模式
export enum MatchMode {
  PUBLIC_POOL = "PUBLIC_POOL",
  SMART_MATCH = "SMART_MATCH",
  DIRECT_ASSIGN = "DIRECT_ASSIGN",
}

// 请求者类型
export enum RequesterType {
  STUDENT_TO_PLANNER = "STUDENT_TO_PLANNER",
  PARENT_TO_PLANNER = "PARENT_TO_PLANNER",
  TEACHER_TO_PLANNER = "TEACHER_TO_PLANNER",
}

// ========== 基础接口类型 ==========

// 用户基础信息
export interface UserBasicInfo {
  id: string;
  name: string;
  avatar?: string;
  email?: string;
  phone?: string;
}

// 租户用户信息
export interface TenantUserInfo {
  user: UserBasicInfo;
  tenantId: string;
}

// 需求信息
export interface RequirementInfo {
  title: string;
  description: string;
  grade: string;
  subjects: string[];
  location: string;
  budget?: number;
  additionalInfo?: string;
}

// 匹配请求
export interface MatchRequest {
  id: string;
  requesterId: string;
  requesterType: RequesterType;
  tenantId: string;
  requirements: RequirementInfo;
  category: RequirementCategory;
  status: MatchRequestStatus;
  urgency: number;
  mode: MatchMode;
  createdAt: string;
  updatedAt: string;
  expiredAt?: string;
  matchedPlannerId?: string;
  matchedAt?: string;
  confirmedAt?: string;
  cancelledAt?: string;
  cancelReason?: string;
  
  // 关联数据
  requester?: TenantUserInfo;
  matchedPlanner?: PlannerProfile;
  responses?: MatchResponse[];
  _count?: {
    responses: number;
    views: number;
  };
}

// 规划师档案
export interface PlannerProfile {
  id: string;
  tenantUserId: string;
  title?: string;
  specialties: string[];
  experience?: number;
  introduction?: string;
  rating?: number;
  successRate?: number;
  totalMatches?: number;
  activeMatches?: number;
  
  // 匹配配置
  matchProfile?: {
    isAcceptingMatch: boolean;
    preferredGrades: string[];
    preferredSubjects: string[];
    preferredLocations: string[];
    responseTime: number;
    maxActiveMatches: number;
    workingHours?: {
      start: string;
      end: string;
      timezone: string;
    };
  };
  
  // 关联数据
  tenantUser: TenantUserInfo;
  _count?: {
    students: number;
    completedMatches: number;
    reviews: number;
  };
}

// 匹配响应
export interface MatchResponse {
  id: string;
  requestId: string;
  plannerId: string;
  message?: string;
  proposedPrice?: number;
  estimatedDuration?: number;
  availableTime?: string;
  status: "PENDING" | "ACCEPTED" | "REJECTED";
  createdAt: string;
  respondedAt?: string;
  
  // 关联数据
  planner: PlannerProfile;
  request: MatchRequest;
}

// 匹配推荐
export interface MatchRecommendation {
  id: string;
  requestId: string;
  plannerId: string;
  score: number;
  reasons: MatchReasons;
  details: MatchDetails;
  createdAt: string;
  
  // 关联数据
  planner: PlannerProfile;
  request: MatchRequest;
}

// 匹配原因
export interface MatchReasons {
  gradeMatch: boolean;
  subjectMatch: boolean;
  locationMatch: boolean;
  hasCapacity: boolean;
  fastResponse: boolean;
  priceMatch: boolean;
  experienceMatch: boolean;
  ratingMatch: boolean;
}

// 匹配详情
export interface MatchDetails {
  currentLoad: number;
  responseTimeScore: number;
  capacityScore: number;
  experienceScore: number;
  ratingScore: number;
  locationDistance?: number;
  priceCompatibility?: number;
}

// ========== 统计相关类型 ==========

// 匹配统计
export interface MatchStatistics {
  total: number;
  pending: number;
  matching: number;
  matched: number;
  confirmed: number;
  cancelled: number;
  expired: number;
  successRate: number;
  averageResponseTime: number;
  averageMatchTime: number;
  
  // 按分类统计
  byCategory: Record<RequirementCategory, {
    count: number;
    successRate: number;
    averageResponseTime: number;
  }>;
  
  // 按时间统计
  byTime: {
    daily: Array<{
      date: string;
      count: number;
      successCount: number;
    }>;
    weekly: Array<{
      week: string;
      count: number;
      successCount: number;
    }>;
    monthly: Array<{
      month: string;
      count: number;
      successCount: number;
    }>;
  };
}

// 规划师统计
export interface PlannerStatistics {
  totalPlanners: number;
  activePlanners: number;
  averageRating: number;
  averageSuccessRate: number;
  averageResponseTime: number;
  
  // 按分类统计
  byCategory: Record<RequirementCategory, {
    plannerCount: number;
    averageRating: number;
    averageSuccessRate: number;
  }>;
  
  // 排行榜
  topPlanners: Array<{
    planner: PlannerProfile;
    rank: number;
    score: number;
    metrics: {
      rating: number;
      successRate: number;
      responseTime: number;
      completedMatches: number;
    };
  }>;
}

// ========== API 相关类型 ==========

// 分页参数
export interface PaginationParams {
  page: number;
  pageSize: number;
}

// 筛选参数
export interface MatchFilters {
  searchQuery?: string;
  category?: RequirementCategory;
  status?: MatchRequestStatus;
  grade?: string;
  subjects?: string[];
  location?: string;
  urgencyMin?: number;
  urgencyMax?: number;
  budgetMin?: number;
  budgetMax?: number;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// 规划师筛选参数
export interface PlannerFilters {
  searchQuery?: string;
  specialties?: string[];
  grades?: string[];
  subjects?: string[];
  locations?: string[];
  ratingMin?: number;
  experienceMin?: number;
  isAcceptingMatch?: boolean;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// ========== 表单相关类型 ==========

// 匹配请求表单数据
export interface MatchRequestFormData {
  category: RequirementCategory;
  title: string;
  description: string;
  grade: string;
  subjects: string[];
  location: string;
  budget?: number;
  urgency: number;
  expiredAt?: Date;
  mode: MatchMode;
  additionalInfo?: string;
}

// 规划师档案表单数据
export interface PlannerProfileFormData {
  title?: string;
  specialties: string[];
  experience?: number;
  introduction?: string;
  preferredGrades: string[];
  preferredSubjects: string[];
  preferredLocations: string[];
  responseTime: number;
  maxActiveMatches: number;
  isAcceptingMatch: boolean;
}

// ========== 配置相关类型 ==========

// 分类配置
export interface CategoryConfig {
  label: string;
  description: string;
  icon: any;
  color: string;
}

// 状态配置
export interface StatusConfig {
  label: string;
  color: string;
  icon: any;
  description: string;
}

// 模式配置
export interface ModeConfig {
  label: string;
  description: string;
  icon: any;
}
