// 请求日志中间件
import { NextResponse } from "next/server";
import type { MiddlewareFunction } from "./types";

// 日志中间件 - 记录请求日志和性能指标
export const loggingMiddleware: MiddlewareFunction = async (context) => {
  const { request } = context;
  const startTime = Date.now();

  // 将开始时间保存到上下文中
  context.startTime = startTime;

  // 记录请求日志
  const { method, url, headers } = request;
  const userAgent = headers.get("user-agent") || "Unknown";
  const ip =
    headers.get("x-forwarded-for") || headers.get("x-real-ip") || "Unknown";

  console.log(`[请求开始] ${new Date().toISOString()}`);
  console.log(`  方法: ${method}`);
  console.log(`  URL: ${url}`);
  console.log(`  IP: ${ip}`);
  console.log(`  User-Agent: ${userAgent}`);

  // 这个中间件不会阻断请求，只记录日志
  return;
};

// 性能统计中间件 - 记录执行时间
export const performanceMiddleware: MiddlewareFunction = async (context) => {
  if (!context.startTime) {
    context.startTime = Date.now();
  }

  // 在响应头中添加性能指标
  const duration = Date.now() - context.startTime;

  if (context.response) {
    context.response.headers.set("X-Response-Time", `${duration}ms`);
    context.response.headers.set("X-Request-ID", `req_${context.startTime}`);
  }

  console.log(`[请求完成] 耗时: ${duration}ms`);

  return;
};

// 请求日志组合中间件
export const requestLoggingMiddleware: MiddlewareFunction = async (context) => {
  // 先记录请求开始
  await loggingMiddleware(context);

  // 不返回响应，继续执行后续中间件
  return;
};
