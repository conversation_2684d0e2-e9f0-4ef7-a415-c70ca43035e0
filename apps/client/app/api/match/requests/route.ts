import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

const authService = new ClientAuthService(prisma);
const matchService = new MatchService(prisma);
/**
 * GET /api/match/requests
 * 获取匹配请求列表
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const status = searchParams.get("status") as any;
    const mode = searchParams.get("mode") as any;
    const category = searchParams.get("category") as any;
    const tenantId = searchParams.get("tenantId")!;
    const requesterId = searchParams.get("requesterId")!;

    const result = await matchService.getMatchRequests({
      tenantId,
      requesterId,
      status,
      mode,
      category,
      page,
      pageSize,
    });

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("获取匹配请求失败:", error);
    return NextResponse.json(
      { error: error.message || "获取匹配请求失败" },
      { status: 500 },
    );
  }
}

/**
 * POST /api/match/requests
 * 创建匹配请求
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userResult = await authService.verifySession(token);

    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // 验证必填字段
    if (!body.requesterType || !body.requirements) {
      return NextResponse.json({ error: "缺少必填字段" }, { status: 400 });
    }

    const matchRequest = await matchService.createMatchRequest({
      requesterId: body.requesterId || userResult.user.id,
      requesterType: body.requesterType,
      tenantId: body.tenantId,
      requirements: body.requirements,
      category: body.category,
      urgency: body.urgency,
      mode: body.mode,
      expiredAt: body.expiredAt ? new Date(body.expiredAt) : undefined,
      metadata: body.metadata,
    });

    return NextResponse.json({
      data: matchRequest,
      success: true,
      message: "匹配请求创建成功",
    });
  } catch (error: any) {
    console.error("创建匹配请求失败:", error);
    return NextResponse.json(
      { message: error.message || "创建匹配请求失败" },
      { status: 500 },
    );
  }
}
