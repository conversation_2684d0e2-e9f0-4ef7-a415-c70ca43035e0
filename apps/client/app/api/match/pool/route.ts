import { NextRequest, NextResponse } from "next/server";
import { MatchService, ClientAuthService, prisma } from "@workspace/database";
import { AUTH_CONFIG, extractTokenFromHeader } from "@/config/auth.config";

/**
 * GET /api/match/pool
 * 获取匹配大厅列表（公开抢单池）
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get(AUTH_CONFIG.HEADERS.AUTHORIZATION);
    const token =
      extractTokenFromHeader(authHeader) ||
      request.cookies.get(AUTH_CONFIG.COOKIES.ACCESS_TOKEN)?.value;

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const authService = new ClientAuthService(prisma);
    const userResult = await authService.verifySession(token);
    
    if (!userResult.success || !userResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // TODO: 需要验证当前用户是否是规划师
    const searchParams = request.nextUrl.searchParams;
    const plannerId = searchParams.get("plannerId");
    
    if (!plannerId) {
      return NextResponse.json(
        { error: "缺少规划师ID" },
        { status: 400 }
      );
    }

    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    
    // 过滤条件
    const filters: any = {};
    const grades = searchParams.get("grades");
    if (grades) filters.grades = grades.split(",");
    
    const subjects = searchParams.get("subjects");
    if (subjects) filters.subjects = subjects.split(",");
    
    const locations = searchParams.get("locations");
    if (locations) filters.locations = locations.split(",");
    
    const minBudget = searchParams.get("minBudget");
    if (minBudget) filters.minBudget = parseFloat(minBudget);
    
    const maxBudget = searchParams.get("maxBudget");
    if (maxBudget) filters.maxBudget = parseFloat(maxBudget);

    const matchService = new MatchService(prisma);
    const result = await matchService.getMatchPool({
      plannerId,
      filters,
      page,
      pageSize,
    });

    return NextResponse.json(result);
  } catch (error: any) {
    console.error("获取匹配池失败:", error);
    return NextResponse.json(
      { error: error.message || "获取匹配池失败" },
      { status: 500 }
    );
  }
}

