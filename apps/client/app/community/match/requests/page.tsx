"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Plus, Search, Filter, Eye, MessageSquare, Calendar, MapPin, User } from "lucide-react";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";

import { RequirementCategorySelector, RequirementCategory, CATEGORY_CONFIG } from "@/components/match/RequirementCategorySelector";
import { apiClient } from "@/utils/api";

// 匹配请求状态
enum MatchRequestStatus {
  PENDING = "PENDING",
  MATCHING = "MATCHING", 
  MATCHED = "MATCHED",
  CONFIRMED = "CONFIRMED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
}

const STATUS_CONFIG = {
  [MatchRequestStatus.PENDING]: { label: "待匹配", color: "bg-yellow-500" },
  [MatchRequestStatus.MATCHING]: { label: "匹配中", color: "bg-blue-500" },
  [MatchRequestStatus.MATCHED]: { label: "已匹配", color: "bg-green-500" },
  [MatchRequestStatus.CONFIRMED]: { label: "已确认", color: "bg-purple-500" },
  [MatchRequestStatus.CANCELLED]: { label: "已取消", color: "bg-gray-500" },
  [MatchRequestStatus.EXPIRED]: { label: "已过期", color: "bg-red-500" },
};

interface MatchRequest {
  id: string;
  title: string;
  requirements: {
    title: string;
    description: string;
    grade: string;
    subjects: string[];
    location: string;
    budget?: number;
  };
  category: RequirementCategory;
  status: MatchRequestStatus;
  urgency: number;
  mode: string;
  createdAt: string;
  expiredAt?: string;
  matchedPlanner?: {
    tenantUser: {
      user: {
        name: string;
        avatar?: string;
      };
    };
  };
  responses: any[];
  _count?: {
    responses: number;
  };
}

export default function MatchRequestsPage() {
  const router = useRouter();
  const [requests, setRequests] = useState<MatchRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<RequirementCategory | undefined>();
  const [selectedStatus, setSelectedStatus] = useState<MatchRequestStatus | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // 获取匹配请求列表
  const fetchRequests = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: "10",
      });

      if (selectedCategory) {
        params.append("category", selectedCategory);
      }
      if (selectedStatus) {
        params.append("status", selectedStatus);
      }

      const result = await apiClient.request(`/api/match/requests?${params}`);
      
      if (result.success) {
        setRequests(result.data.items || []);
        setTotalPages(result.data.totalPages || 1);
      }
    } catch (error) {
      console.error("获取匹配请求失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [currentPage, selectedCategory, selectedStatus]);

  // 过滤请求
  const filteredRequests = requests.filter(request => {
    if (!searchQuery) return true;
    
    return (
      request.requirements.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.requirements.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const handleCreateRequest = () => {
    router.push("/community/match/create");
  };

  const handleViewRequest = (requestId: string) => {
    router.push(`/community/match/requests/${requestId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getUrgencyColor = (urgency: number) => {
    if (urgency <= 3) return "bg-green-500";
    if (urgency <= 6) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">匹配请求</h1>
            <p className="text-muted-foreground">管理您的匹配请求</p>
          </div>
          <Button onClick={handleCreateRequest} className="gap-2">
            <Plus className="h-4 w-4" />
            创建请求
          </Button>
        </div>

        {/* 筛选器 */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索 */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索请求..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* 分类筛选 */}
          <div className="w-full sm:w-64">
            <RequirementCategorySelector
              value={selectedCategory}
              onValueChange={setSelectedCategory}
              placeholder="所有分类"
              showDescription={false}
              size="default"
            />
          </div>

          {/* 状态筛选 */}
          <Select
            value={selectedStatus}
            onValueChange={(value) => setSelectedStatus(value as MatchRequestStatus)}
          >
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue placeholder="所有状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                <SelectItem key={status} value={status}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 活动筛选条件 */}
        {(selectedCategory || selectedStatus || searchQuery) && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">筛选条件:</span>
            {searchQuery && (
              <Badge variant="secondary" className="gap-1">
                搜索: {searchQuery}
                <button
                  onClick={() => setSearchQuery("")}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {selectedCategory && (
              <Badge variant="secondary" className="gap-1">
                分类: {CATEGORY_CONFIG[selectedCategory].label}
                <button
                  onClick={() => setSelectedCategory(undefined)}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {selectedStatus && (
              <Badge variant="secondary" className="gap-1">
                状态: {STATUS_CONFIG[selectedStatus].label}
                <button
                  onClick={() => setSelectedStatus(undefined)}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* 请求列表 */}
      {loading ? (
        <div className="grid gap-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {filteredRequests.length > 0 ? (
            <div className="grid gap-4">
              {filteredRequests.map((request) => {
                const categoryConfig = CATEGORY_CONFIG[request.category];
                const statusConfig = STATUS_CONFIG[request.status];
                const CategoryIcon = categoryConfig.icon;

                return (
                  <Card key={request.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          {/* 分类图标 */}
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${categoryConfig.color}`}>
                            <CategoryIcon className="h-5 w-5 text-white" />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <CardTitle className="text-lg truncate">
                                {request.requirements.title}
                              </CardTitle>
                              {/* 紧急程度 */}
                              <div className={`w-2 h-2 rounded-full ${getUrgencyColor(request.urgency)}`} />
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {request.requirements.grade}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {request.requirements.location}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(request.createdAt)}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* 状态和响应数 */}
                        <div className="flex items-center gap-2">
                          {request._count?.responses && request._count.responses > 0 && (
                            <Badge variant="outline" className="gap-1">
                              <MessageSquare className="h-3 w-3" />
                              {request._count.responses}
                            </Badge>
                          )}
                          <Badge variant="outline" className="gap-1">
                            <div className={`w-2 h-2 rounded-full ${statusConfig.color}`} />
                            {statusConfig.label}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      <CardDescription className="mb-4 line-clamp-2">
                        {request.requirements.description}
                      </CardDescription>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{categoryConfig.label}</Badge>
                          {request.requirements.subjects.slice(0, 2).map((subject) => (
                            <Badge key={subject} variant="outline" className="text-xs">
                              {subject}
                            </Badge>
                          ))}
                          {request.requirements.subjects.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{request.requirements.subjects.length - 2}
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-2">
                          {/* 匹配的规划师 */}
                          {request.matchedPlanner && (
                            <div className="flex items-center gap-2 text-sm">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={request.matchedPlanner.tenantUser.user.avatar} />
                                <AvatarFallback>
                                  {request.matchedPlanner.tenantUser.user.name.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-muted-foreground">
                                {request.matchedPlanner.tenantUser.user.name}
                              </span>
                            </div>
                          )}

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewRequest(request.id)}
                            className="gap-1"
                          >
                            <Eye className="h-3 w-3" />
                            查看
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                <Search className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">暂无匹配请求</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || selectedCategory || selectedStatus
                  ? "没有找到符合条件的请求，试试调整筛选条件"
                  : "还没有任何匹配请求，创建第一个请求吧！"}
              </p>
              <Button onClick={handleCreateRequest} className="gap-2">
                <Plus className="h-4 w-4" />
                创建请求
              </Button>
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <span className="text-sm text-muted-foreground">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}

