"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  ArrowLeft,
  MapPin,
  User,
  Clock,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { Separator } from "@workspace/ui/components/separator";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@workspace/ui/components/alert";
import { Skeleton } from "@workspace/ui/components/skeleton";

import {
  RequirementCategory,
  CATEGORY_CONFIG,
} from "@/components/match/RequirementCategorySelector";
import { apiClient } from "@/utils/api";
import { useClientUser } from "@/hooks/useClientUser";

// 匹配请求状态
enum MatchRequestStatus {
  PENDING = "PENDING",
  MATCHING = "MATCHING",
  MATCHED = "MATCHED",
  CONFIRMED = "CONFIRMED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
}

const STATUS_CONFIG = {
  [MatchRequestStatus.PENDING]: {
    label: "待匹配",
    color: "bg-yellow-500",
    description: "您的请求已提交，等待匹配合适的规划师",
  },
  [MatchRequestStatus.MATCHING]: {
    label: "匹配中",
    color: "bg-blue-500",
    description: "正在为您寻找最合适的规划师，请耐心等待",
  },
  [MatchRequestStatus.MATCHED]: {
    label: "已匹配",
    color: "bg-green-500",
    description: "已为您匹配到规划师，请确认是否接受",
  },
  [MatchRequestStatus.CONFIRMED]: {
    label: "已确认",
    color: "bg-purple-500",
    description: "您已确认接受匹配的规划师，可以开始沟通了",
  },
  [MatchRequestStatus.CANCELLED]: {
    label: "已取消",
    color: "bg-gray-500",
    description: "该匹配请求已被取消",
  },
  [MatchRequestStatus.EXPIRED]: {
    label: "已过期",
    color: "bg-red-500",
    description: "该匹配请求已过期，请创建新的请求",
  },
};

// 匹配模式
enum MatchMode {
  PUBLIC_POOL = "PUBLIC_POOL",
  SMART_MATCH = "SMART_MATCH",
  DIRECT_ASSIGN = "DIRECT_ASSIGN",
}

const MODE_CONFIG = {
  [MatchMode.PUBLIC_POOL]: {
    label: "公开抢单",
    description: "您的请求将公开展示，规划师可主动申请",
  },
  [MatchMode.SMART_MATCH]: {
    label: "智能匹配",
    description: "系统将自动为您匹配最合适的规划师",
  },
  [MatchMode.DIRECT_ASSIGN]: {
    label: "直接指派",
    description: "管理员直接指派规划师",
  },
};

interface MatchRequest {
  id: string;
  requirements: {
    title: string;
    description: string;
    grade: string;
    subjects: string[];
    location: string;
    budget?: number;
    additionalInfo?: string;
  };
  category: RequirementCategory;
  status: MatchRequestStatus;
  urgency: number;
  mode: MatchMode;
  createdAt: string;
  expiredAt?: string;
  matchedAt?: string;
  confirmedAt?: string;
  matchedPlanner?: {
    id: string;
    tenantUser: {
      user: {
        id: string;
        name: string;
        avatar?: string;
      };
    };
    specialties?: string[];
    introduction?: string;
    rating?: number;
  };
  responses: {
    id: string;
    plannerId: string;
    message?: string;
    respondedAt: string;
    isAccepted: boolean;
    isRejected: boolean;
    planner: {
      id: string;
      tenantUser: {
        user: {
          id: string;
          name: string;
          avatar?: string;
        };
      };
      specialties?: string[];
      introduction?: string;
      rating?: number;
    };
  }[];
  evaluations: {
    id: string;
    rating: number;
    comment?: string;
    tags: string[];
    createdAt: string;
  }[];
}

export default function MatchRequestDetailPage() {
  const router = useRouter();
  const { id } = useParams();
  const { user } = useClientUser();
  const [request, setRequest] = useState<MatchRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // 获取匹配请求详情
  const fetchRequestDetail = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await apiClient.request(`/api/match/requests/${id}`);

      if (result.success) {
        setRequest(result.data);
      } else {
        setError(result.error || "获取请求详情失败");
      }
    } catch (error) {
      console.error("获取匹配请求详情失败:", error);
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchRequestDetail();
    }
  }, [id]);

  // 取消匹配请求
  const handleCancelRequest = async () => {
    if (!confirm("确定要取消该匹配请求吗？此操作不可恢复。")) {
      return;
    }

    setActionLoading(true);
    try {
      const result = await apiClient.request(`/api/match/requests/${id}`, {
        method: "DELETE",
      });

      if (result.success) {
        fetchRequestDetail();
      } else {
        setError(result.error || "取消请求失败");
      }
    } catch (error) {
      console.error("取消匹配请求失败:", error);
      setError("网络错误，请稍后重试");
    } finally {
      setActionLoading(false);
    }
  };

  // 确认匹配
  const handleConfirmMatch = async () => {
    if (!confirm("确定要接受该规划师的匹配吗？")) {
      return;
    }

    setActionLoading(true);
    try {
      const result = await apiClient.request(`/api/match/confirm/${id}`, {
        method: "POST",
      });

      if (result.success) {
        fetchRequestDetail();
      } else {
        setError(result.error || "确认匹配失败");
      }
    } catch (error) {
      console.error("确认匹配失败:", error);
      setError("网络错误，请稍后重试");
    } finally {
      setActionLoading(false);
    }
  };

  // 接受规划师响应
  const handleAcceptResponse = async (responseId: string) => {
    if (!confirm("确定要接受该规划师的响应吗？")) {
      return;
    }

    setActionLoading(true);
    try {
      const result = await apiClient.request(
        `/api/match/response/${responseId}`,
        {
          method: "POST",
          body: JSON.stringify({
            action: "accept",
          }),
        },
      );

      if (result.success) {
        fetchRequestDetail();
      } else {
        setError(result.error || "接受响应失败");
      }
    } catch (error) {
      console.error("接受规划师响应失败:", error);
      setError("网络错误，请稍后重试");
    } finally {
      setActionLoading(false);
    }
  };

  // 拒绝规划师响应
  const handleRejectResponse = async (responseId: string) => {
    if (!confirm("确定要拒绝该规划师的响应吗？")) {
      return;
    }

    setActionLoading(true);
    try {
      const result = await apiClient.request(
        `/api/match/response/${responseId}`,
        {
          method: "POST",
          body: JSON.stringify({
            action: "reject",
          }),
        },
      );

      if (result.success) {
        fetchRequestDetail();
      } else {
        setError(result.error || "拒绝响应失败");
      }
    } catch (error) {
      console.error("拒绝规划师响应失败:", error);
      setError("网络错误，请稍后重试");
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "未设置";
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getUrgencyText = (urgency: number) => {
    if (urgency <= 3) return "低";
    if (urgency <= 6) return "中";
    return "高";
  };

  const getUrgencyColor = (urgency: number) => {
    if (urgency <= 3) return "bg-green-500";
    if (urgency <= 6) return "bg-yellow-500";
    return "bg-red-500";
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Button variant="ghost" className="mb-4 gap-2">
            <ArrowLeft className="h-4 w-4" />
            返回
          </Button>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>获取请求详情失败</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <div className="mt-4 flex justify-center">
          <Button onClick={fetchRequestDetail}>重试</Button>
        </div>
      </div>
    );
  }

  if (!request) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>请求不存在</AlertTitle>
          <AlertDescription>该匹配请求不存在或已被删除</AlertDescription>
        </Alert>

        <div className="mt-4 flex justify-center">
          <Button onClick={() => router.push("/community/match/requests")}>
            返回列表
          </Button>
        </div>
      </div>
    );
  }

  const categoryConfig = CATEGORY_CONFIG[request.category];
  const statusConfig = STATUS_CONFIG[request.status];
  const modeConfig = MODE_CONFIG[request.mode];
  const CategoryIcon = categoryConfig.icon;
  const hasResponses = request.responses && request.responses.length > 0;
  const canCancel =
    request.status === MatchRequestStatus.PENDING ||
    request.status === MatchRequestStatus.MATCHING;
  const canConfirm =
    request.status === MatchRequestStatus.MATCHED && request.matchedPlanner;
  const isOwner = user?.id === request.id;

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold">{request.requirements.title}</h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline" className="gap-1">
                <div className={`w-2 h-2 rounded-full ${statusConfig.color}`} />
                {statusConfig.label}
              </Badge>
              <Badge variant="secondary">{categoryConfig.label}</Badge>
              <Badge variant="outline" className="gap-1">
                <Clock className="h-3 w-3" />
                {modeConfig.label}
              </Badge>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            {canCancel && (
              <Button
                variant="outline"
                onClick={handleCancelRequest}
                disabled={actionLoading}
              >
                取消请求
              </Button>
            )}
            {canConfirm && (
              <Button onClick={handleConfirmMatch} disabled={actionLoading}>
                确认匹配
              </Button>
            )}
            {request.status === MatchRequestStatus.EXPIRED && (
              <Button onClick={() => router.push("/community/match/create")}>
                创建新请求
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 状态提示 */}
      <Alert
        className={`mb-6 ${statusConfig.color} bg-opacity-10 border-${statusConfig.color}`}
      >
        <AlertTitle>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${statusConfig.color}`} />
            {statusConfig.label}
          </div>
        </AlertTitle>
        <AlertDescription>{statusConfig.description}</AlertDescription>
      </Alert>

      <div className="grid gap-6 md:grid-cols-3">
        {/* 左侧：请求详情 */}
        <div className="md:col-span-2 space-y-6">
          {/* 需求详情 */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <div
                  className={`w-8 h-8 rounded-lg flex items-center justify-center ${categoryConfig.color}`}
                >
                  <CategoryIcon className="h-4 w-4 text-white" />
                </div>
                <CardTitle>需求详情</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 基本信息 */}
              <div>
                <h3 className="font-medium mb-2">需求描述</h3>
                <p className="whitespace-pre-line text-muted-foreground">
                  {request.requirements.description}
                </p>
              </div>

              <Separator />

              {/* 学习信息 */}
              <div>
                <h3 className="font-medium mb-3">学习信息</h3>
                <div className="grid gap-3 sm:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      年级：
                    </span>
                    <span>{request.requirements.grade}</span>
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm text-muted-foreground">
                        相关科目：
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {request.requirements.subjects.map((subject) => (
                        <Badge key={subject} variant="outline">
                          {subject}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* 服务要求 */}
              <div>
                <h3 className="font-medium mb-3">服务要求</h3>
                <div className="grid gap-3 sm:grid-cols-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      地区：
                    </span>
                    <span>{request.requirements.location}</span>
                  </div>
                  {request.requirements.budget && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        预算：
                      </span>
                      <span>￥{request.requirements.budget}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      紧急程度：
                    </span>
                    <div className="flex items-center gap-1">
                      <div
                        className={`w-2 h-2 rounded-full ${getUrgencyColor(request.urgency)}`}
                      />
                      <span>
                        {getUrgencyText(request.urgency)}（{request.urgency}
                        /10）
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      匹配模式：
                    </span>
                    <span>{modeConfig.label}</span>
                  </div>
                </div>
              </div>

              {request.requirements.additionalInfo && (
                <>
                  <Separator />
                  <div>
                    <h3 className="font-medium mb-2">补充信息</h3>
                    <p className="whitespace-pre-line text-muted-foreground">
                      {request.requirements.additionalInfo}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* 已匹配规划师 */}
          {request.matchedPlanner && (
            <Card>
              <CardHeader>
                <CardTitle>已匹配规划师</CardTitle>
                <CardDescription>
                  {request.status === MatchRequestStatus.CONFIRMED
                    ? "已确认的规划师，可以开始沟通了"
                    : "系统为您匹配的规划师，请确认是否接受"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={request.matchedPlanner.tenantUser.user.avatar}
                    />
                    <AvatarFallback>
                      {request.matchedPlanner.tenantUser.user.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-medium">
                      {request.matchedPlanner.tenantUser.user.name}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      {request.matchedPlanner.specialties?.join("、") ||
                        "暂无专长信息"}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {request.matchedPlanner.introduction || "暂无简介"}
                    </p>
                  </div>
                </div>

                {canConfirm && (
                  <div className="mt-4 flex justify-end">
                    <Button
                      onClick={handleConfirmMatch}
                      disabled={actionLoading}
                      className="gap-1"
                    >
                      <CheckCircle2 className="h-4 w-4" />
                      确认匹配
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 规划师响应列表 */}
          {hasResponses && request.mode === MatchMode.PUBLIC_POOL && (
            <Card>
              <CardHeader>
                <CardTitle>收到的响应 ({request.responses.length})</CardTitle>
                <CardDescription>
                  规划师对您的需求提交的响应，您可以选择接受或拒绝
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {request.responses.map((response) => (
                    <div key={response.id} className="border rounded-lg p-4">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={response.planner.tenantUser.user.avatar}
                          />
                          <AvatarFallback>
                            {response.planner.tenantUser.user.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium">
                              {response.planner.tenantUser.user.name}
                            </h3>
                            <div className="text-sm text-muted-foreground">
                              响应时间：{formatDate(response.respondedAt)}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {response.planner.specialties?.join("、") ||
                              "暂无专长信息"}
                          </p>
                          {response.message && (
                            <div className="bg-muted p-3 rounded-md text-sm mb-3">
                              {response.message}
                            </div>
                          )}

                          {/* 响应状态和操作按钮 */}
                          <div className="flex justify-between items-center">
                            <div>
                              {response.isAccepted && (
                                <Badge variant="success">已接受</Badge>
                              )}
                              {response.isRejected && (
                                <Badge variant="destructive">已拒绝</Badge>
                              )}
                            </div>
                            {!response.isAccepted &&
                              !response.isRejected &&
                              request.status === MatchRequestStatus.PENDING && (
                                <div className="flex gap-2">
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() =>
                                      handleRejectResponse(response.id)
                                    }
                                    disabled={actionLoading}
                                  >
                                    拒绝
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() =>
                                      handleAcceptResponse(response.id)
                                    }
                                    disabled={actionLoading}
                                  >
                                    接受
                                  </Button>
                                </div>
                              )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧：侧边栏信息 */}
        <div className="space-y-6">
          {/* 请求状态 */}
          <Card>
            <CardHeader>
              <CardTitle>请求状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">当前状态</span>
                  <Badge
                    variant="outline"
                    className={`gap-1 border-${statusConfig.color}`}
                  >
                    <div
                      className={`w-2 h-2 rounded-full ${statusConfig.color}`}
                    />
                    {statusConfig.label}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">创建时间</span>
                  <span>{formatDate(request.createdAt)}</span>
                </div>
                {request.expiredAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">过期时间</span>
                    <span>{formatDate(request.expiredAt)}</span>
                  </div>
                )}
                {request.matchedAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">匹配时间</span>
                    <span>{formatDate(request.matchedAt)}</span>
                  </div>
                )}
                {request.confirmedAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">确认时间</span>
                    <span>{formatDate(request.confirmedAt)}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 响应统计 */}
          {request.mode === MatchMode.PUBLIC_POOL && (
            <Card>
              <CardHeader>
                <CardTitle>响应统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">收到响应</span>
                    <span>{request.responses.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">匹配模式</span>
                    <span>{modeConfig.label}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 操作按钮卡片 */}
          <Card>
            <CardHeader>
              <CardTitle>操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {canCancel && (
                  <Button
                    variant="destructive"
                    className="w-full"
                    onClick={handleCancelRequest}
                    disabled={actionLoading}
                  >
                    取消请求
                  </Button>
                )}
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/community/match/requests")}
                >
                  返回列表
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/community/match/create")}
                >
                  创建新请求
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
