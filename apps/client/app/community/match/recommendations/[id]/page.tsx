"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON>ef<PERSON>, Refresh<PERSON>w, Settings } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import { toast } from "sonner";

import { SmartMatchRecommendations } from "@/components/match/SmartMatchRecommendations";
import { apiClient } from "@/utils/api";

interface MatchRequest {
  id: string;
  requesterId: string;
  requesterType: string;
  requirements: {
    title: string;
    description: string;
    grade: string;
    subjects: string[];
    location: string;
    budget?: number;
    additionalInfo?: string;
  };
  category: string;
  urgency: number;
  status: string;
  mode: string;
  createdAt: string;
  expiredAt?: string;
}

export default function MatchRecommendationsPage() {
  const params = useParams();
  const router = useRouter();
  const matchRequestId = params.id as string;

  const [matchRequest, setMatchRequest] = useState<MatchRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (matchRequestId) {
      loadMatchRequest();
    }
  }, [matchRequestId]);

  const loadMatchRequest = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await apiClient.request(`/api/match/requests/${matchRequestId}`);
      
      if (result.success) {
        setMatchRequest(result.data);
      } else {
        setError(result.message || "获取匹配请求失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handlePlannerSelect = (plannerId: string) => {
    toast.success("已选择规划师", {
      description: "正在为您安排匹配，请稍候...",
    });
    
    // 跳转到匹配详情页面
    router.push(`/workspace/match/requests/${matchRequestId}`);
  };

  const handleContactPlanner = (plannerId: string) => {
    // 这里可以打开聊天窗口或联系方式
    toast.info("联系功能", {
      description: "即将开放规划师直接联系功能",
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: "待匹配", variant: "secondary" as const },
      MATCHING: { label: "匹配中", variant: "default" as const },
      MATCHED: { label: "已匹配", variant: "success" as const },
      CONFIRMED: { label: "已确认", variant: "success" as const },
      CANCELLED: { label: "已取消", variant: "destructive" as const },
      EXPIRED: { label: "已过期", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getModeLabel = (mode: string) => {
    return mode === "SMART_MATCH" ? "智能匹配" : "公开抢单";
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" disabled>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
            <div className="h-8 w-48 bg-muted rounded animate-pulse" />
          </div>
          <div className="space-y-4">
            <div className="h-32 bg-muted rounded animate-pulse" />
            <div className="h-96 bg-muted rounded animate-pulse" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !matchRequest) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">获取匹配请求失败</h1>
          <p className="text-muted-foreground">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button variant="outline" onClick={() => router.back()}>
              返回
            </Button>
            <Button onClick={loadMatchRequest}>
              重试
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>
        
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold">智能匹配推荐</h1>
            <p className="text-muted-foreground mt-2">
              基于您的需求为您推荐最合适的规划师
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              匹配设置
            </Button>
            <Button variant="outline" size="sm" onClick={loadMatchRequest}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新推荐
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：需求信息 */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">需求信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">{matchRequest.requirements.title}</h4>
                <p className="text-sm text-muted-foreground">
                  {matchRequest.requirements.description}
                </p>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">状态</span>
                  {getStatusBadge(matchRequest.status)}
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">匹配模式</span>
                  <span>{getModeLabel(matchRequest.mode)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">年级</span>
                  <span>{matchRequest.requirements.grade}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">地区</span>
                  <span>{matchRequest.requirements.location}</span>
                </div>

                {matchRequest.requirements.budget && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">预算</span>
                    <span>¥{matchRequest.requirements.budget}</span>
                  </div>
                )}

                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">紧急程度</span>
                  <Badge variant="outline">
                    {matchRequest.urgency}/10
                  </Badge>
                </div>
              </div>

              {matchRequest.requirements.subjects.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <div className="text-sm text-muted-foreground mb-2">相关科目</div>
                    <div className="flex flex-wrap gap-1">
                      {matchRequest.requirements.subjects.map((subject) => (
                        <Badge key={subject} variant="secondary" className="text-xs">
                          {subject}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {matchRequest.requirements.additionalInfo && (
                <>
                  <Separator />
                  <div>
                    <div className="text-sm text-muted-foreground mb-2">补充信息</div>
                    <p className="text-sm">{matchRequest.requirements.additionalInfo}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：推荐列表 */}
        <div className="lg:col-span-2">
          <SmartMatchRecommendations
            matchRequestId={matchRequestId}
            onPlannerSelect={handlePlannerSelect}
            onContactPlanner={handleContactPlanner}
          />
        </div>
      </div>
    </div>
  );
}

