"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { User<PERSON>he<PERSON> } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Skeleton } from "@workspace/ui/components/skeleton";

import { MatchFilterBar } from "@/components/match/MatchFilterBar";
import { PlannerCard } from "@/components/match/common/MatchCard";
import { MatchPagination } from "@/components/match/common/MatchPagination";
import { DataEmpty } from "@/components/match/common/MatchEmptyState";

import { useMatchPlanners } from "@/hooks/useMatchData";
import { type MatchFilters } from "@/types/match.types";

export default function PlannersListPage() {
  const router = useRouter();
  const [sortBy, setSortBy] = useState("rating");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // 使用重构后的hook
  const {
    planners,
    loading,
    error,
    pagination,
    filters,
    totalPages,
    totalCount,
    updateFilters,
    updatePagination,
    refresh,
  } = useMatchPlanners({
    sortBy,
    sortOrder,
  });

  const handleFilterChange = (newFilters: Partial<MatchFilters>) => {
    updateFilters(newFilters);
  };

  const handleSortChange = (value: string) => {
    if (value === sortBy) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(value);
      setSortOrder("desc");
    }
    updateFilters({ sortBy: value, sortOrder });
  };

  const handleViewDetails = (plannerId: string) => {
    router.push(`/community/match/planner/${plannerId}`);
  };

  const handleApplyMatch = (plannerId: string) => {
    // 这里可以打开匹配申请对话框或跳转到申请页面
    console.log("申请匹配规划师:", plannerId);
  };

  const handleClearFilters = () => {
    updateFilters({
      searchQuery: "",
      grade: undefined,
      subjects: [],
      location: undefined,
    });
  };

  const hasFilters = !!(
    filters.searchQuery || 
    filters.grade || 
    filters.subjects?.length || 
    filters.location
  );

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="flex flex-col gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">规划师列表</h1>
          <p className="text-muted-foreground">浏览所有可匹配的规划师</p>
        </div>

        {/* 筛选器和排序 */}
        <div className="flex flex-col gap-4">
          <MatchFilterBar
            onFilterChange={handleFilterChange}
            initialFilters={filters}
            showGrade={false}
            searchPlaceholder="搜索规划师..."
          />

          {/* 排序 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {hasFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                >
                  清除筛选
                </Button>
              )}
            </div>
            
            <div className="w-full sm:w-48">
              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger>
                  <SelectValue placeholder="排序方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rating">评分</SelectItem>
                  <SelectItem value="successRate">成功率</SelectItem>
                  <SelectItem value="experience">经验年限</SelectItem>
                  <SelectItem value="responseTime">响应时间</SelectItem>
                  <SelectItem value="students">服务人数</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      {!loading && (
        <div className="mb-4 text-sm text-muted-foreground">
          共找到 {totalCount} 位规划师
        </div>
      )}

      {/* 规划师列表 */}
      {loading ? (
        <div className="space-y-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="p-6 border rounded-lg">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-16 w-16 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="text-center">
                    <Skeleton className="h-4 w-12 mb-1" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                  <div className="text-center">
                    <Skeleton className="h-4 w-12 mb-1" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                </div>
              </div>
              <Skeleton className="h-16 w-full mb-3" />
              <div className="flex gap-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-18" />
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={refresh} variant="outline">
            重试
          </Button>
        </div>
      ) : planners.length > 0 ? (
        <div className="space-y-6">
          {planners.map((planner) => (
            <PlannerCard
              key={planner.id}
              planner={planner}
              onViewDetails={handleViewDetails}
              onApplyMatch={handleApplyMatch}
              actions={
                <Button
                  variant="default"
                  size="sm"
                  className="gap-1"
                  disabled={!planner.matchProfile?.isAcceptingMatch}
                  onClick={() => handleApplyMatch(planner.id)}
                >
                  <UserCheck className="h-4 w-4" />
                  {planner.matchProfile?.isAcceptingMatch
                    ? "申请匹配"
                    : "暂不接单"}
                </Button>
              }
            />
          ))}
        </div>
      ) : (
        <DataEmpty
          type="planners"
          searchQuery={filters.searchQuery}
          hasFilters={hasFilters}
          onClearFilters={handleClearFilters}
          onRefresh={refresh}
        />
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="mt-6">
          <MatchPagination
            currentPage={pagination.page}
            totalPages={totalPages}
            totalCount={totalCount}
            pageSize={pagination.pageSize}
            onPageChange={(page) => updatePagination({ page })}
            onPageSizeChange={(pageSize) => updatePagination({ pageSize, page: 1 })}
          />
        </div>
      )}
    </div>
  );
}
