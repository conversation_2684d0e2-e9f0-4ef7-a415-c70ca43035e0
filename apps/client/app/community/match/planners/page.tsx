"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>Pin,
  <PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON><PERSON>ck,
  Calendar,
  Award,
  Search,
} from "lucide-react";

import { Location, Subject, GradeLevel } from "@/constants/match-constants";
import {
  MatchFilterBar,
  MatchFilters,
} from "@/components/match/MatchFilterBar";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@workspace/ui/components/accordion";

import { apiClient } from "@/utils/api";

interface PlannerProfile {
  id: string;
  tenantUser: {
    user: {
      name: string;
      avatar?: string;
    };
  };
  title?: string;
  specialties: string[];
  experience?: number;
  introduction?: string;
  rating?: number;
  successRate?: number;
  matchProfile?: {
    isAcceptingMatch: boolean;
    preferredGrades: string[];
    preferredSubjects: string[];
    preferredLocations: string[];
    responseTime: number;
  };
  _count?: {
    students: number;
  };
}

export default function PlannersListPage() {
  const [planners, setPlanners] = useState<PlannerProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<MatchFilters>({
    searchQuery: "",
    subjects: [],
    grade: undefined,
    location: undefined,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState("rating");
  const [sortOrder, setSortOrder] = useState("desc");

  // 获取规划师列表
  const fetchPlanners = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: "10",
        sortBy,
        sortOrder,
      });

      if (filters.grade) {
        params.append("grade", filters.grade);
      }
      if (filters.subjects.length > 0) {
        filters.subjects.forEach((subject) => {
          params.append("subject", subject);
        });
      }
      if (filters.location) {
        params.append("location", filters.location);
      }

      // 模拟API调用
      // const result = await apiClient.request(`/api/match/planners?${params}`);

      // 模拟数据
      const mockPlanners = Array.from({ length: 10 }).map((_, i) => ({
        id: `planner-${i + 1}`,
        tenantUser: {
          user: {
            name: `规划师 ${i + 1}`,
            avatar:
              i % 3 === 0 ? undefined : `/avatars/avatar-${(i % 5) + 1}.png`,
          },
        },
        title: ["资深规划师", "高级规划师", "首席规划师", "特约规划师"][i % 4],
        specialties: [
          ["数学", "物理", "化学"],
          ["语文", "英语", "历史"],
          ["学业规划", "志愿填报", "留学规划"],
          ["心理辅导", "学习能力", "生涯规划"],
        ][i % 4],
        experience: Math.floor(Math.random() * 10) + 3,
        introduction: `拥有${Math.floor(Math.random() * 10) + 3}年教育经验，专注于${["学科辅导", "学业规划", "生涯发展", "考试备考"][i % 4]}领域，致力于帮助学生发掘潜能，实现目标。`,
        rating: (3.5 + Math.random() * 1.5).toFixed(1),
        successRate: Math.floor(Math.random() * 30 + 70),
        matchProfile: {
          isAcceptingMatch: i % 5 !== 0,
          preferredGrades: ["初中", "高中", "大学"],
          preferredSubjects: ["数学", "英语", "物理", "化学"].slice(
            0,
            (i % 3) + 2,
          ),
          preferredLocations: ["北京", "上海", "广州", "深圳"].slice(
            0,
            (i % 3) + 1,
          ),
          responseTime: Math.floor(Math.random() * 24) + 1,
        },
        _count: {
          students: Math.floor(Math.random() * 50) + 10,
        },
      }));

      setPlanners(mockPlanners);
      setTotalPages(5); // 模拟总页数
      setLoading(false);
    } catch (error) {
      console.error("获取规划师列表失败:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlanners();
  }, [currentPage, filters, sortBy, sortOrder]);

  // 过滤规划师
  const filteredPlanners = planners.filter((planner) => {
    if (!filters.searchQuery) return true;

    return (
      planner.tenantUser.user.name
        .toLowerCase()
        .includes(filters.searchQuery.toLowerCase()) ||
      planner.specialties.some((s) =>
        s.toLowerCase().includes(filters.searchQuery.toLowerCase()),
      ) ||
      (planner.introduction &&
        planner.introduction
          .toLowerCase()
          .includes(filters.searchQuery.toLowerCase()))
    );
  });

  const handleSortChange = (value: string) => {
    if (value === sortBy) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(value);
      setSortOrder("desc");
    }
  };

  // 显示星级评分
  const renderStars = (rating?: number) => {
    if (!rating) return "暂无评分";

    return (
      <div className="flex items-center">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${i < Math.floor(rating) ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
          />
        ))}
        <span className="ml-1 text-sm font-medium">{rating}</span>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 页面头部 */}
      <div className="flex flex-col gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">规划师列表</h1>
          <p className="text-muted-foreground">浏览所有可匹配的规划师</p>
        </div>

        {/* 筛选器和排序 */}
        <div className="flex flex-col gap-4">
          <MatchFilterBar
            onFilterChange={setFilters}
            initialFilters={filters}
            showGrade={false}
            searchPlaceholder="搜索规划师..."
          />

          {/* 排序 */}
          <div className="w-full sm:w-48 ml-auto">
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger>
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">评分</SelectItem>
                <SelectItem value="successRate">成功率</SelectItem>
                <SelectItem value="experience">经验年限</SelectItem>
                <SelectItem value="responseTime">响应时间</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 规划师列表 */}
      {loading ? (
        <div className="grid gap-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-16 w-16 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          {filteredPlanners.length > 0 ? (
            <div className="grid gap-6">
              {filteredPlanners.map((planner) => (
                <Card key={planner.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-16 w-16 border-2 border-primary/10">
                          <AvatarImage src={planner.tenantUser.user.avatar} />
                          <AvatarFallback className="text-lg">
                            {planner.tenantUser.user.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>

                        <div>
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-xl">
                              {planner.tenantUser.user.name}
                            </CardTitle>
                            {!planner.matchProfile?.isAcceptingMatch && (
                              <Badge
                                variant="outline"
                                className="text-yellow-600 border-yellow-300 bg-yellow-50"
                              >
                                休息中
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                            {planner.title && <span>{planner.title}</span>}
                            {planner.experience && (
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {planner.experience}年经验
                              </span>
                            )}
                            {planner.matchProfile?.preferredLocations[0] && (
                              <span className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {planner.matchProfile.preferredLocations[0]}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-wrap items-center gap-6 sm:gap-4 ml-0 sm:ml-auto">
                        <div className="flex flex-col items-center">
                          <div className="text-sm text-muted-foreground">
                            评分
                          </div>
                          {renderStars(Number(planner.rating))}
                        </div>

                        <div className="flex flex-col items-center">
                          <div className="text-sm text-muted-foreground">
                            成功率
                          </div>
                          <span className="font-medium">
                            {planner.successRate || "—"}%
                          </span>
                        </div>

                        <div className="flex flex-col items-center">
                          <div className="text-sm text-muted-foreground">
                            响应时间
                          </div>
                          <span className="font-medium">
                            {planner.matchProfile?.responseTime || "—"}小时
                          </span>
                        </div>

                        <div className="flex flex-col items-center">
                          <div className="text-sm text-muted-foreground">
                            已服务
                          </div>
                          <span className="font-medium">
                            {planner._count?.students || 0}人
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pb-3">
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-2 mb-2">
                        {planner.specialties.map((specialty) => (
                          <Badge key={specialty} variant="secondary">
                            {specialty}
                          </Badge>
                        ))}
                      </div>

                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {planner.introduction || "暂无简介"}
                      </p>
                    </div>

                    <Accordion
                      type="single"
                      collapsible
                      className="border rounded-md"
                    >
                      <AccordionItem value="details" className="border-none">
                        <AccordionTrigger className="py-2 px-4 text-sm hover:no-underline">
                          查看详细信息
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-3">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <h4 className="font-medium mb-1 flex items-center gap-1">
                                <BookOpen className="h-4 w-4" />
                                擅长年级
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {planner.matchProfile?.preferredGrades.map(
                                  (grade) => (
                                    <Badge
                                      key={grade}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {grade}
                                    </Badge>
                                  ),
                                )}
                              </div>
                            </div>

                            <div>
                              <h4 className="font-medium mb-1 flex items-center gap-1">
                                <Award className="h-4 w-4" />
                                擅长科目
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {planner.matchProfile?.preferredSubjects.map(
                                  (subject) => (
                                    <Badge
                                      key={subject}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {subject}
                                    </Badge>
                                  ),
                                )}
                              </div>
                            </div>

                            <div>
                              <h4 className="font-medium mb-1 flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                服务地区
                              </h4>
                              <div className="flex flex-wrap gap-1">
                                {planner.matchProfile?.preferredLocations.map(
                                  (location) => (
                                    <Badge
                                      key={location}
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {location}
                                    </Badge>
                                  ),
                                )}
                              </div>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </CardContent>

                  <CardFooter className="flex justify-end pt-2 pb-4">
                    <Button
                      variant="default"
                      size="sm"
                      className="gap-1"
                      disabled={!planner.matchProfile?.isAcceptingMatch}
                    >
                      <UserCheck className="h-4 w-4" />
                      {planner.matchProfile?.isAcceptingMatch
                        ? "申请匹配"
                        : "暂不接单"}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                <Search className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">暂无规划师</h3>
              <p className="text-muted-foreground mb-4">
                {filters.searchQuery || filters.subjects.length > 0 || filters.location
                  ? "没有找到符合条件的规划师，试试调整筛选条件"
                  : "暂无规划师信息"}
              </p>
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                上一页
              </Button>
              <span className="text-sm text-muted-foreground">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                }
                disabled={currentPage === totalPages}
              >
                下一页
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
