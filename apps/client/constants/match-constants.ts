/**
 * 匹配模块常量文件
 * 包含所有匹配模块需要的公共常量定义，以提高代码复用性
 */

import {
  BookOpen,
  Users,
  Calendar,
  Target,
  HelpCircle,
  Briefcase,
  FileText,
  Globe,
  Languages,
  MapPin,
  PlaneTakeoff,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Timer,
} from "lucide-react";

import {
  RequirementCategory,
  MatchRequestStatus,
  MatchMode,
  RequesterType,
  type CategoryConfig,
  type StatusConfig,
  type ModeConfig,
} from "@/types/match.types";

// =============== 需求分类相关 ===============

// 分类配置
export const CATEGORY_CONFIG: Record<RequirementCategory, CategoryConfig> = {
  [RequirementCategory.LEARNING_ABILITY]: {
    label: "学习力",
    description: "学习能力提升、学习方法指导、注意力训练等",
    icon: BookOpen,
    color: "bg-blue-500",
  },
  [RequirementCategory.PSYCHOLOGICAL_SUPPORT]: {
    label: "心理辅导",
    description: "情绪管理、压力缓解、心理健康等",
    icon: HelpCircle,
    color: "bg-pink-500",
  },
  [RequirementCategory.COLLEGE_APPLICATION]: {
    label: "志愿填报",
    description: "高考志愿填报、专业选择、院校咨询等",
    icon: Target,
    color: "bg-purple-500",
  },
  [RequirementCategory.SPECIAL_ADMISSION]: {
    label: "特殊类型招生",
    description: "自主招生、综合评价、三位一体等特殊招生类型",
    icon: Star,
    color: "bg-amber-500",
  },
  [RequirementCategory.HONGKONG_MACAO]: {
    label: "港澳留学",
    description: "港澳地区的大学申请与规划",
    icon: MapPin,
    color: "bg-red-500",
  },
  [RequirementCategory.ENGLISH_COUNTRIES]: {
    label: "英语系国家留学",
    description: "美国、英国、加拿大、澳洲等英语国家留学",
    icon: Globe,
    color: "bg-indigo-500",
  },
  [RequirementCategory.NON_ENGLISH_COUNTRIES]: {
    label: "小语种国家留学",
    description: "日本、韩国、法国、德国等非英语国家留学",
    icon: Languages,
    color: "bg-emerald-500",
  },
  [RequirementCategory.ACADEMIC_PLANNING]: {
    label: "学业规划",
    description: "学习计划制定、课程选择、成绩提升等",
    icon: Calendar,
    color: "bg-blue-600",
  },
  [RequirementCategory.CAREER_PLANNING]: {
    label: "职业规划",
    description: "职业发展路径、专业选择、就业指导等",
    icon: Briefcase,
    color: "bg-green-600",
  },
  [RequirementCategory.STUDY_ABROAD]: {
    label: "出国留学",
    description: "留学整体规划、海外升学指导等",
    icon: PlaneTakeoff,
    color: "bg-sky-500",
  },
  [RequirementCategory.EXAM_PREPARATION]: {
    label: "考试备考",
    description: "标准化考试准备、考试技巧指导等",
    icon: FileText,
    color: "bg-orange-500",
  },
  [RequirementCategory.EXTRACURRICULAR]: {
    label: "课外活动规划",
    description: "社团活动、志愿服务、竞赛准备等",
    icon: Users,
    color: "bg-violet-500",
  },
};

// 状态配置
export const STATUS_CONFIG: Record<MatchRequestStatus, StatusConfig> = {
  [MatchRequestStatus.PENDING]: {
    label: "待匹配",
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    icon: Clock,
    description: "等待系统匹配合适的规划师",
  },
  [MatchRequestStatus.MATCHING]: {
    label: "匹配中",
    color: "bg-blue-100 text-blue-800 border-blue-200",
    icon: Timer,
    description: "正在为您寻找最合适的规划师",
  },
  [MatchRequestStatus.MATCHED]: {
    label: "已匹配",
    color: "bg-green-100 text-green-800 border-green-200",
    icon: CheckCircle,
    description: "已找到合适的规划师，等待确认",
  },
  [MatchRequestStatus.CONFIRMED]: {
    label: "已确认",
    color: "bg-emerald-100 text-emerald-800 border-emerald-200",
    icon: CheckCircle,
    description: "匹配已确认，可以开始服务",
  },
  [MatchRequestStatus.CANCELLED]: {
    label: "已取消",
    color: "bg-gray-100 text-gray-800 border-gray-200",
    icon: XCircle,
    description: "匹配请求已被取消",
  },
  [MatchRequestStatus.EXPIRED]: {
    label: "已过期",
    color: "bg-red-100 text-red-800 border-red-200",
    icon: AlertCircle,
    description: "匹配请求已过期",
  },
};

// 匹配模式配置
export const MODE_CONFIG: Record<MatchMode, ModeConfig> = {
  [MatchMode.PUBLIC_POOL]: {
    label: "公开抢单",
    description: "发布到公开池，让规划师主动响应",
    icon: Users,
  },
  [MatchMode.SMART_MATCH]: {
    label: "智能匹配",
    description: "系统自动为您推荐最合适的规划师",
    icon: Target,
  },
  [MatchMode.DIRECT_ASSIGN]: {
    label: "直接指派",
    description: "直接指定规划师",
    icon: CheckCircle,
  },
};

// =============== 年级选项相关 ===============

// 年级枚举
export enum GradeLevel {
  PRIMARY_1 = "primary-1",
  PRIMARY_2 = "primary-2",
  PRIMARY_3 = "primary-3",
  PRIMARY_4 = "primary-4",
  PRIMARY_5 = "primary-5",
  PRIMARY_6 = "primary-6",
  JUNIOR_1 = "junior-1",
  JUNIOR_2 = "junior-2",
  JUNIOR_3 = "junior-3",
  SENIOR_1 = "senior-1",
  SENIOR_2 = "senior-2",
  SENIOR_3 = "senior-3",
  UNIVERSITY = "university",
  ADULT = "adult",
}

// 年级配置
export const GRADE_CONFIG = {
  [GradeLevel.PRIMARY_1]: { label: "小学一年级", group: "小学" },
  [GradeLevel.PRIMARY_2]: { label: "小学二年级", group: "小学" },
  [GradeLevel.PRIMARY_3]: { label: "小学三年级", group: "小学" },
  [GradeLevel.PRIMARY_4]: { label: "小学四年级", group: "小学" },
  [GradeLevel.PRIMARY_5]: { label: "小学五年级", group: "小学" },
  [GradeLevel.PRIMARY_6]: { label: "小学六年级", group: "小学" },
  [GradeLevel.JUNIOR_1]: { label: "初中一年级", group: "初中" },
  [GradeLevel.JUNIOR_2]: { label: "初中二年级", group: "初中" },
  [GradeLevel.JUNIOR_3]: { label: "初中三年级", group: "初中" },
  [GradeLevel.SENIOR_1]: { label: "高中一年级", group: "高中" },
  [GradeLevel.SENIOR_2]: { label: "高中二年级", group: "高中" },
  [GradeLevel.SENIOR_3]: { label: "高中三年级", group: "高中" },
  [GradeLevel.UNIVERSITY]: { label: "大学", group: "大学" },
  [GradeLevel.ADULT]: { label: "成人", group: "其他" },
};

// 年级分组
export const GRADE_GROUPS = [
  {
    label: "小学",
    options: [
      GradeLevel.PRIMARY_1,
      GradeLevel.PRIMARY_2,
      GradeLevel.PRIMARY_3,
      GradeLevel.PRIMARY_4,
      GradeLevel.PRIMARY_5,
      GradeLevel.PRIMARY_6,
    ],
  },
  {
    label: "初中",
    options: [GradeLevel.JUNIOR_1, GradeLevel.JUNIOR_2, GradeLevel.JUNIOR_3],
  },
  {
    label: "高中",
    options: [GradeLevel.SENIOR_1, GradeLevel.SENIOR_2, GradeLevel.SENIOR_3],
  },
  {
    label: "其他",
    options: [GradeLevel.UNIVERSITY, GradeLevel.ADULT],
  },
];

// 年级选项数组 (用于下拉选择)
export const GRADE_OPTIONS = Object.entries(GRADE_CONFIG).map(
  ([value, config]) => ({
    value,
    label: config.label,
    group: config.group,
  }),
);

// =============== 科目选项相关 ===============

// 科目枚举
export enum Subject {
  CHINESE = "chinese",
  MATH = "math",
  ENGLISH = "english",
  PHYSICS = "physics",
  CHEMISTRY = "chemistry",
  BIOLOGY = "biology",
  HISTORY = "history",
  GEOGRAPHY = "geography",
  POLITICS = "politics",
  PROGRAMMING = "programming",
  ART = "art",
  MUSIC = "music",
  SPORTS = "sports",
}

// 科目配置
export const SUBJECT_CONFIG = {
  [Subject.CHINESE]: { label: "语文", group: "主科" },
  [Subject.MATH]: { label: "数学", group: "主科" },
  [Subject.ENGLISH]: { label: "英语", group: "主科" },
  [Subject.PHYSICS]: { label: "物理", group: "理科" },
  [Subject.CHEMISTRY]: { label: "化学", group: "理科" },
  [Subject.BIOLOGY]: { label: "生物", group: "理科" },
  [Subject.HISTORY]: { label: "历史", group: "文科" },
  [Subject.GEOGRAPHY]: { label: "地理", group: "文科" },
  [Subject.POLITICS]: { label: "政治", group: "文科" },
  [Subject.PROGRAMMING]: { label: "编程", group: "特长" },
  [Subject.ART]: { label: "美术", group: "特长" },
  [Subject.MUSIC]: { label: "音乐", group: "特长" },
  [Subject.SPORTS]: { label: "体育", group: "特长" },
};

// 科目分组
export const SUBJECT_GROUPS = [
  {
    label: "主科",
    options: [Subject.CHINESE, Subject.MATH, Subject.ENGLISH],
  },
  {
    label: "理科",
    options: [Subject.PHYSICS, Subject.CHEMISTRY, Subject.BIOLOGY],
  },
  {
    label: "文科",
    options: [Subject.HISTORY, Subject.GEOGRAPHY, Subject.POLITICS],
  },
  {
    label: "特长",
    options: [Subject.PROGRAMMING, Subject.ART, Subject.MUSIC, Subject.SPORTS],
  },
];

// 科目选项数组 (用于下拉选择)
export const SUBJECT_OPTIONS = Object.entries(SUBJECT_CONFIG).map(
  ([value, config]) => ({
    value,
    label: config.label,
    group: config.group,
  }),
);

// =============== 地区选项相关 ===============

// 地区枚举
export enum Location {
  BEIJING = "beijing",
  SHANGHAI = "shanghai",
  GUANGZHOU = "guangzhou",
  SHENZHEN = "shenzhen",
  HANGZHOU = "hangzhou",
  NANJING = "nanjing",
  CHENGDU = "chengdu",
  WUHAN = "wuhan",
  XIAN = "xian",
  ONLINE = "online",
}

// 地区配置
export const LOCATION_CONFIG = {
  [Location.BEIJING]: { label: "北京", group: "华北" },
  [Location.SHANGHAI]: { label: "上海", group: "华东" },
  [Location.GUANGZHOU]: { label: "广州", group: "华南" },
  [Location.SHENZHEN]: { label: "深圳", group: "华南" },
  [Location.HANGZHOU]: { label: "杭州", group: "华东" },
  [Location.NANJING]: { label: "南京", group: "华东" },
  [Location.CHENGDU]: { label: "成都", group: "西南" },
  [Location.WUHAN]: { label: "武汉", group: "华中" },
  [Location.XIAN]: { label: "西安", group: "西北" },
  [Location.ONLINE]: { label: "在线教学", group: "其他" },
};

// 地区分组
export const LOCATION_GROUPS = [
  {
    label: "华北",
    options: [Location.BEIJING],
  },
  {
    label: "华东",
    options: [Location.SHANGHAI, Location.HANGZHOU, Location.NANJING],
  },
  {
    label: "华南",
    options: [Location.GUANGZHOU, Location.SHENZHEN],
  },
  {
    label: "华中",
    options: [Location.WUHAN],
  },
  {
    label: "西南",
    options: [Location.CHENGDU],
  },
  {
    label: "西北",
    options: [Location.XIAN],
  },
  {
    label: "其他",
    options: [Location.ONLINE],
  },
];

// 地区选项数组 (用于下拉选择)
export const LOCATION_OPTIONS = Object.entries(LOCATION_CONFIG).map(
  ([value, config]) => ({
    value,
    label: config.label,
    group: config.group,
  }),
);

// =============== 其他工具函数 ===============

// 紧急程度配置
export const URGENCY_LABELS = {
  1: "不急",
  2: "较缓",
  3: "一般",
  4: "较急",
  5: "普通",
  6: "重要",
  7: "紧急",
  8: "很急",
  9: "非常急",
  10: "极急",
};

// 根据紧急程度获取颜色
export function getUrgencyColor(urgency: number) {
  if (urgency <= 3) return "bg-green-500";
  if (urgency <= 6) return "bg-yellow-500";
  return "bg-red-500";
}

// 格式化日期函数
export function formatDate(
  dateString: string,
  options: Intl.DateTimeFormatOptions = {},
) {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  };

  const mergedOptions = { ...defaultOptions, ...options };
  return new Date(dateString).toLocaleDateString("zh-CN", mergedOptions);
}
