// API 请求封装
export class ApiClient {
  private static instance: ApiClient;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value: any) => void;
    reject: (reason: any) => void;
  }> = [];

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  async request<T = any>(
    url: string,
    options: RequestInit = {},
  ): Promise<{ success: boolean; data?: T; message?: string; errors?: any }> {
    const config: RequestInit = {
      ...options,
      headers: {
        "Content-Type": "application/json",
        Host: `*.${window.location.host}`, // 确保请求头中包含正确的Host
        ...options.headers,
      },
      credentials: "include", // 确保包含 cookies，token在httpOnly cookie中
    };

    try {
      let response = await fetch(url, config);

      // 如果 access token 过期，尝试刷新
      // 排除认证相关的API，避免死循环
      if (
        response.status === 401 &&
        !url.includes("/login") &&
        !url.includes("/refresh") &&
        !url.includes("/logout") &&
        !url.includes("/api/auth/") // 排除所有认证API
      ) {
        if (this.isRefreshing) {
          // 如果正在刷新，将请求加入队列
          return new Promise((resolve, reject) => {
            this.failedQueue.push({ resolve, reject });
          });
        }

        this.isRefreshing = true;

        try {
          const refreshResult = await this.refreshToken();
          if (refreshResult.success) {
            // 刷新成功，处理队列中的请求
            this.processQueue(null);
            // 重新发送原始请求（cookie已被服务端自动更新）
            response = await fetch(url, config);
          } else {
            // 刷新失败，拒绝所有队列中的请求
            this.processQueue(new Error("Token刷新失败"));
            // 跳转到登录页
            this.redirectToLogin();
            return { success: false, message: "登录已过期，请重新登录" };
          }
        } finally {
          this.isRefreshing = false;
        }
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("API请求失败:", error);
      return { success: false, message: "网络错误，请稍后重试" };
    }
  }

  private async refreshToken(): Promise<{ success: boolean }> {
    try {
      const response = await fetch("/api/auth/refresh", {
        method: "POST",
        credentials: "include", // 包含 cookies
      });

      if (response.ok) {
        return { success: true };
      } else {
        return { success: false };
      }
    } catch (error) {
      console.error("Token刷新失败:", error);
      return { success: false };
    }
  }

  private processQueue(error: Error | null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(null);
      }
    });

    this.failedQueue = [];
  }

  private redirectToLogin() {
    // 避免在服务端执行
    if (typeof window !== "undefined") {
      const currentPath = window.location.pathname;
      window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
    }
  }
}

// 导出单例实例
export const apiClient = ApiClient.getInstance();
