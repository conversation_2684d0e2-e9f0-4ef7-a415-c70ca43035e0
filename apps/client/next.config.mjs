/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: [
    "@workspace/ui",
    "@workspace/database",
    "@workspace/ioredis",
  ],
  experimental: {
    viewTransition: true,
    reactCompiler: true,
  },
  typescript: {
    // !! 仅在生产构建时忽略 TS 错误
    ignoreBuildErrors: process.env.NODE_ENV === "production",
  },
  images: {
    domains: [
      "baihuabei-1258807705.cos.ap-chongqing.myqcloud.com",
      "rysyclub-1258807705.cos.ap-chongqing.myqcloud.com",
    ],
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN", // Only allows embedding from the same origin
            // Or use 'ALLOW-FROM https://example.com' to allow specific domains
          },
        ],
      },
    ];
  },
};

export default nextConfig;
