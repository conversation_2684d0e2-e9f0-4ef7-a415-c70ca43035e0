"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import { Card, CardContent } from "@workspace/ui/components/card";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import { useClientUser } from "../hooks/useClientUser";
import { apiClient } from "@/utils/api";
import { toast } from "sonner";
import Link from "next/link";

// zod验证schema
const loginSchema = z.object({
  email: z
    .string()
    .min(1, "请输入邮箱地址")
    .email("请输入有效的邮箱地址")
    .max(254, "邮箱地址过长"),
  password: z
    .string()
    .min(6, "密码长度至少6位")
    .max(100, "密码长度不能超过100位")
    .regex(/^[A-Za-z0-9@#$%^&+=!]*$/, "密码包含非法字符"),
  remember: z.boolean().optional().default(false),
});

// 表单数据类型
type FormData = z.infer<typeof loginSchema>;

// 登录响应数据类型
interface LoginResponse {
  user: {
    id: string;
    name: string; // 显示名称
    username: string; // 用户名
    email: string;
    avatar?: string;
  };
  token?: string;
  refreshToken?: string;
  tenants?: Array<{
    id: string;
    name: string;
    type: string;
    role: string;
  }>;
  needTenantSelection?: boolean;
}

// 错误信息类型
interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
    remember: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentView, setCurrentView] = useState<"login" | "tenant-selection">(
    "login",
  );
  const [availableTenants, setAvailableTenants] = useState<
    LoginResponse["tenants"]
  >([]);
  const [selectedTenant, setSelectedTenant] = useState<string>("");
  const [loginResult, setLoginResult] = useState<LoginResponse | null>(null);
  const { login, setTenants, switchTenant } = useClientUser();
  const router = useRouter();

  // 使用zod进行客户端表单验证
  const validateForm = (): boolean => {
    try {
      loginSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {};
        error.errors.forEach((err) => {
          if (err.path[0] === "email") {
            newErrors.email = err.message;
          } else if (err.path[0] === "password") {
            newErrors.password = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  // 使用zod实时验证单个字段
  const validateField = (
    field: keyof FormData,
    value: string | boolean,
  ): string | undefined => {
    try {
      const fieldSchema = loginSchema.shape[field];
      fieldSchema.parse(value);
      return undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message;
      }
      return undefined;
    }
  };

  // 处理输入变化
  const handleInputChange =
    (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = field === "remember" ? e.target.checked : e.target.value;
      setFormData((prev) => ({ ...prev, [field]: value }));

      // 清除对应字段的错误
      if (errors[field as keyof FormErrors]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }));
      }
    };

  // 处理输入失去焦点时的验证
  const handleBlur =
    (field: keyof FormData) => (e: React.FocusEvent<HTMLInputElement>) => {
      if (field === "remember") return;

      const value = e.target.value;
      const error = validateField(field, value);

      if (error) {
        setErrors((prev) => ({ ...prev, [field]: error }));
      }
    };

  // 租户选择处理
  const handleTenantSelection = async () => {
    if (!selectedTenant || !loginResult) return;

    setIsLoading(true);

    try {
      // 调用租户选择API
      const currentTenant = await switchTenant(selectedTenant);

      if (!!currentTenant) {
        // 检查 URL 参数中是否有重定向地址
        const urlParams = new URLSearchParams(window.location.search);
        const redirectTo = urlParams.get("redirect");

        // 跳转到指定页面或主页
        router.push(
          redirectTo && redirectTo !== "/" ? redirectTo : "/workspace",
        );
      } else {
        setErrors({ general: "选择租户失败，请重试" });
      }
    } catch (error) {
      console.error("选择租户失败:", error);
      setErrors({ general: "网络连接失败，请重试" });
    } finally {
      setIsLoading(false);
    }
  };

  // 表单提交处理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 客户端验证
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await apiClient.request<LoginResponse>("/api/auth/login", {
        method: "POST",
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          remember: formData.remember,
        }),
      });

      if (result.success && result.data) {
        // Token 已经在服务端设置到 httpOnly Cookie 中，包括 access_token 和 refresh_token

        // 更新用户上下文
        login(result.data.user);

        // 更新租户信息
        if (result.data.tenants) {
          setTenants(result.data.tenants);
        }

        // 检查 URL 参数中是否有重定向地址
        const urlParams = new URLSearchParams(window.location.search);
        const redirectTo = urlParams.get("redirect");

        // 检查是否需要选择租户
        if (
          result.data.needTenantSelection &&
          result.data.tenants &&
          result.data.tenants.length > 1
        ) {
          // 切换到租户选择视图
          setAvailableTenants(result.data.tenants);
          setSelectedTenant(result.data.tenants[0]?.id || "");
          setLoginResult(result.data);
          setCurrentView("tenant-selection");
        } else {
          // 直接跳转到指定页面或主页
          router.push(redirectTo || "/community");
        }
      } else {
        // 处理登录失败
        if (result.errors) {
          setErrors(result.errors);
        } else {
          setErrors({ general: result.message || "登录失败，请重试" });
        }
      }
    } catch (error) {
      console.error("登录请求失败:", error);

      // 只显示网络错误，让用户手动重试
      setErrors({
        general: "网络连接失败，请检查网络后重试",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className={cn("flex flex-col gap-6 md:min-w-[880px]", className)}
      {...props}
    >
      <Card className="overflow-hidden p-0">
        <CardContent className="grid p-0 md:grid-cols-2">
          <div className="relative overflow-hidden">
            <AnimatePresence mode="wait">
              {currentView === "login" ? (
                <motion.form
                  key="login"
                  className="p-6 md:p-8"
                  onSubmit={handleSubmit}
                  initial={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col items-center text-center">
                      <h1 className="text-2xl font-bold">欢迎回来</h1>
                      <p className="text-muted-foreground text-balance">
                        登录您的全域全面规划账户
                      </p>
                    </div>

                    {/* 通用错误信息 */}
                    {errors.general && (
                      <Alert variant="destructive">
                        <AlertDescription>{errors.general}</AlertDescription>
                      </Alert>
                    )}

                    {/* 邮箱输入 */}
                    <div className="grid gap-3">
                      <Label htmlFor="email">邮箱地址</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="请输入邮箱地址"
                        value={formData.email}
                        onChange={handleInputChange("email")}
                        onBlur={handleBlur("email")}
                        className={errors.email ? "border-red-500" : ""}
                        disabled={isLoading}
                      />
                      {errors.email && (
                        <p className="text-sm text-red-500">{errors.email}</p>
                      )}
                    </div>

                    {/* 密码输入 */}
                    <div className="grid gap-3">
                      <div className="flex items-center">
                        <Label htmlFor="password">密码</Label>
                        <a
                          href="#"
                          className="ml-auto text-sm underline-offset-2 hover:underline"
                          onClick={(e) => {
                            e.preventDefault();
                            // TODO: 实现忘记密码功能
                            toast.warning("忘记密码功能正在开发中...");
                          }}
                        >
                          忘记密码？
                        </a>
                      </div>
                      <Input
                        id="password"
                        type="password"
                        placeholder="请输入密码"
                        value={formData.password}
                        onChange={handleInputChange("password")}
                        onBlur={handleBlur("password")}
                        className={errors.password ? "border-red-500" : ""}
                        disabled={isLoading}
                      />
                      {errors.password && (
                        <p className="text-sm text-red-500">
                          {errors.password}
                        </p>
                      )}
                    </div>

                    {/* 记住我选项 */}
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="remember"
                        checked={formData.remember}
                        onCheckedChange={(checked) =>
                          setFormData((prev) => ({
                            ...prev,
                            remember: !!checked,
                          }))
                        }
                        disabled={isLoading}
                      />
                      <Label
                        htmlFor="remember"
                        className="text-sm font-normal cursor-pointer"
                      >
                        记住我的登录状态
                      </Label>
                    </div>

                    {/* 登录按钮 */}
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading}
                    >
                      {isLoading ? "登录中..." : "登录"}
                    </Button>

                    {/* 第三方登录分隔线 */}
                    <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                      <span className="bg-card text-muted-foreground relative z-10 px-2">
                        或使用第三方登录
                      </span>
                    </div>

                    {/* 第三方登录按钮 */}
                    <div className="grid grid-cols-3 gap-4">
                      <Button
                        variant="outline"
                        type="button"
                        className="w-full"
                        disabled={isLoading}
                        onClick={() =>
                          toast.warning("微信登录功能正在开发中...")
                        }
                      >
                        <svg
                          className="w-4 h-4"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M8.5 12.5c-.5 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zm7 0c-.5 0-1-.4-1-1s.4-1 1-1 1 .4 1 1-.4 1-1 1zM12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2z" />
                        </svg>
                        <span className="sr-only">微信登录</span>
                      </Button>
                      <Button
                        variant="outline"
                        type="button"
                        className="w-full"
                        disabled={isLoading}
                        onClick={() => toast.warning("QQ登录功能正在开发中...")}
                      >
                        <svg
                          className="w-4 h-4"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                        </svg>
                        <span className="sr-only">QQ登录</span>
                      </Button>
                      <Button
                        variant="outline"
                        type="button"
                        className="w-full"
                        disabled={isLoading}
                        onClick={() =>
                          toast.warning("支付宝登录功能正在开发中...")
                        }
                      >
                        <svg
                          className="w-4 h-4"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                        </svg>
                        <span className="sr-only">支付宝登录</span>
                      </Button>
                    </div>

                    {/* 注册链接 */}
                    <div className="text-center text-sm">
                      还没有账户？{" "}
                      <a
                        href="#"
                        className="underline underline-offset-4"
                        onClick={(e) => {
                          e.preventDefault();
                          // TODO: 实现注册功能
                          toast.warning("注册功能正在开发中...");
                        }}
                      >
                        立即注册
                      </a>
                    </div>
                  </div>
                </motion.form>
              ) : (
                <motion.div
                  key="tenant-selection"
                  className="p-6 md:p-8"
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 100 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col items-center text-center">
                      <h1 className="text-2xl font-bold">选择工作空间</h1>
                      <p className="text-muted-foreground text-balance">
                        您属于多个工作空间，请选择要登录的工作空间
                      </p>
                    </div>

                    {/* 通用错误信息 */}
                    {errors.general && (
                      <Alert variant="destructive">
                        <AlertDescription>{errors.general}</AlertDescription>
                      </Alert>
                    )}

                    {/* 租户选择 */}
                    <div className="space-y-3">
                      <RadioGroup
                        value={selectedTenant}
                        onValueChange={setSelectedTenant}
                      >
                        {availableTenants?.map((tenant) => (
                          <div key={tenant.id} className="space-y-2">
                            <div className="flex items-center space-x-3">
                              <RadioGroupItem
                                value={tenant.id}
                                id={tenant.id}
                              />
                              <Label
                                htmlFor={tenant.id}
                                className="flex-1 cursor-pointer p-4 rounded-lg border hover:bg-muted/50 transition-colors"
                              >
                                <div className="font-medium text-sm">
                                  {tenant.name}
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                  {tenant.type} · {tenant.role}
                                </div>
                              </Label>
                            </div>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setCurrentView("login");
                          setErrors({});
                        }}
                        disabled={isLoading}
                        className="flex-1"
                      >
                        返回登录
                      </Button>
                      <Button
                        disabled={isLoading || !selectedTenant}
                        className="flex-1"
                      >
                        <Link href="/community">
                          {isLoading ? "进入中..." : "进入社区"}
                        </Link>
                      </Button>
                      <Button
                        onClick={handleTenantSelection}
                        disabled={isLoading || !selectedTenant}
                        className="flex-1"
                      >
                        {isLoading ? "进入中..." : "进入工作空间"}
                      </Button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 右侧图片 */}
          <div className="bg-muted relative hidden md:block">
            <img
              src="/icon-512x512.png"
              alt="全域全面规划"
              className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
            />
          </div>
        </CardContent>
      </Card>

      {/* 服务条款 */}
      <div className="text-muted-foreground text-center text-xs text-balance">
        点击登录即表示您同意我们的{" "}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          服务条款
        </a>{" "}
        和{" "}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          隐私政策
        </a>
        。
      </div>
    </div>
  );
}
