"use client";

import { ReactNode } from "react";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Avatar, AvatarFallback, AvatarImage } from "@workspace/ui/components/avatar";
import { cn } from "@workspace/ui/lib/utils";

// 通用的匹配卡片组件
interface MatchCardProps {
  title: string;
  description?: string;
  avatar?: {
    src?: string;
    fallback: string;
  };
  badges?: Array<{
    text: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
    className?: string;
  }>;
  metadata?: Array<{
    label: string;
    value: string | ReactNode;
    icon?: ReactNode;
  }>;
  actions?: ReactNode;
  children?: ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
}

export function MatchCard({
  title,
  description,
  avatar,
  badges = [],
  metadata = [],
  actions,
  children,
  className,
  onClick,
  hoverable = true,
}: MatchCardProps) {
  return (
    <Card 
      className={cn(
        "overflow-hidden",
        hoverable && "hover:shadow-md transition-shadow cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {avatar && (
              <Avatar className="h-12 w-12 border-2 border-primary/10 flex-shrink-0">
                <AvatarImage src={avatar.src} />
                <AvatarFallback>{avatar.fallback}</AvatarFallback>
              </Avatar>
            )}
            
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <CardTitle className="text-lg truncate">{title}</CardTitle>
                {badges.map((badge, index) => (
                  <Badge 
                    key={index} 
                    variant={badge.variant || "secondary"}
                    className={badge.className}
                  >
                    {badge.text}
                  </Badge>
                ))}
              </div>
              
              {description && (
                <CardDescription className="line-clamp-2">
                  {description}
                </CardDescription>
              )}
            </div>
          </div>
          
          {actions && (
            <div className="flex-shrink-0">
              {actions}
            </div>
          )}
        </div>
        
        {metadata.length > 0 && (
          <div className="flex flex-wrap items-center gap-4 mt-2 text-sm text-muted-foreground">
            {metadata.map((item, index) => (
              <div key={index} className="flex items-center gap-1">
                {item.icon}
                <span className="font-medium">{item.label}:</span>
                <span>{item.value}</span>
              </div>
            ))}
          </div>
        )}
      </CardHeader>
      
      {children && (
        <CardContent className="pb-3">
          {children}
        </CardContent>
      )}
    </Card>
  );
}

// 匹配请求卡片组件
interface MatchRequestCardProps {
  request: {
    id: string;
    title: string;
    requirements: {
      description: string;
      grade: string;
      subjects: string[];
      location: string;
      budget?: number;
    };
    category: string;
    status: string;
    urgency: number;
    createdAt: string;
    matchedPlanner?: {
      tenantUser: {
        user: {
          name: string;
          avatar?: string;
        };
      };
    };
    _count?: {
      responses: number;
    };
  };
  categoryConfig: {
    label: string;
    icon: any;
    color: string;
  };
  statusConfig: {
    label: string;
    color: string;
  };
  onViewDetails?: (id: string) => void;
  actions?: ReactNode;
}

export function MatchRequestCard({
  request,
  categoryConfig,
  statusConfig,
  onViewDetails,
  actions,
}: MatchRequestCardProps) {
  const CategoryIcon = categoryConfig.icon;
  
  return (
    <MatchCard
      title={request.requirements.title || request.title}
      description={request.requirements.description}
      avatar={{
        src: request.matchedPlanner?.tenantUser.user.avatar,
        fallback: request.matchedPlanner?.tenantUser.user.name?.charAt(0) || "?",
      }}
      badges={[
        {
          text: statusConfig.label,
          className: statusConfig.color,
        },
        {
          text: categoryConfig.label,
          variant: "outline",
        },
      ]}
      metadata={[
        {
          label: "年级",
          value: request.requirements.grade,
        },
        {
          label: "地区",
          value: request.requirements.location,
        },
        {
          label: "科目",
          value: request.requirements.subjects.slice(0, 2).join("、") + 
                (request.requirements.subjects.length > 2 ? "等" : ""),
        },
        ...(request.requirements.budget ? [{
          label: "预算",
          value: `¥${request.requirements.budget}`,
        }] : []),
        ...(request._count?.responses ? [{
          label: "响应数",
          value: request._count.responses.toString(),
        }] : []),
      ]}
      actions={actions}
      onClick={() => onViewDetails?.(request.id)}
    >
      <div className="flex items-center gap-2 mb-2">
        <div className={cn("w-6 h-6 rounded-lg flex items-center justify-center", categoryConfig.color)}>
          <CategoryIcon className="h-3 w-3 text-white" />
        </div>
        <span className="text-sm font-medium">{categoryConfig.label}</span>
      </div>
      
      <div className="flex flex-wrap gap-1">
        {request.requirements.subjects.map((subject) => (
          <Badge key={subject} variant="outline" className="text-xs">
            {subject}
          </Badge>
        ))}
      </div>
    </MatchCard>
  );
}

// 规划师卡片组件
interface PlannerCardProps {
  planner: {
    id: string;
    tenantUser: {
      user: {
        name: string;
        avatar?: string;
      };
    };
    title?: string;
    specialties: string[];
    experience?: number;
    introduction?: string;
    rating?: number;
    successRate?: number;
    matchProfile?: {
      isAcceptingMatch: boolean;
      responseTime: number;
    };
    _count?: {
      students: number;
    };
  };
  onViewDetails?: (id: string) => void;
  onApplyMatch?: (id: string) => void;
  actions?: ReactNode;
}

export function PlannerCard({
  planner,
  onViewDetails,
  onApplyMatch,
  actions,
}: PlannerCardProps) {
  const renderStars = (rating?: number) => {
    if (!rating) return "暂无评分";
    
    return (
      <div className="flex items-center gap-1">
        <span className="text-yellow-400">★</span>
        <span>{rating}</span>
      </div>
    );
  };

  return (
    <MatchCard
      title={planner.tenantUser.user.name}
      description={planner.introduction}
      avatar={{
        src: planner.tenantUser.user.avatar,
        fallback: planner.tenantUser.user.name.charAt(0),
      }}
      badges={[
        ...(planner.title ? [{
          text: planner.title,
          variant: "secondary" as const,
        }] : []),
        ...(!planner.matchProfile?.isAcceptingMatch ? [{
          text: "休息中",
          variant: "outline" as const,
          className: "text-yellow-600 border-yellow-300 bg-yellow-50",
        }] : []),
      ]}
      metadata={[
        ...(planner.experience ? [{
          label: "经验",
          value: `${planner.experience}年`,
        }] : []),
        {
          label: "评分",
          value: renderStars(planner.rating),
        },
        ...(planner.successRate ? [{
          label: "成功率",
          value: `${planner.successRate}%`,
        }] : []),
        ...(planner.matchProfile?.responseTime ? [{
          label: "响应时间",
          value: `${planner.matchProfile.responseTime}小时`,
        }] : []),
        ...(planner._count?.students ? [{
          label: "已服务",
          value: `${planner._count.students}人`,
        }] : []),
      ]}
      actions={actions}
      onClick={() => onViewDetails?.(planner.id)}
    >
      <div className="flex flex-wrap gap-1">
        {planner.specialties.map((specialty) => (
          <Badge key={specialty} variant="secondary" className="text-xs">
            {specialty}
          </Badge>
        ))}
      </div>
    </MatchCard>
  );
}
