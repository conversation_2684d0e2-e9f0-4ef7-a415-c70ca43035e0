"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { CalendarIcon, X, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Calendar } from "@workspace/ui/components/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Badge } from "@workspace/ui/components/badge";
import { Slider } from "@workspace/ui/components/slider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { cn } from "@workspace/ui/lib/utils";

import {
  RequirementCategorySelector,
  RequirementCategory,
} from "./RequirementCategorySelector";
import { LocationSelector } from "./LocationSelector";
import { Location } from "@/constants/match-constants";
import { apiClient } from "@/utils/api";
import { useClientUser } from "@/hooks/useClientUser";

// 表单验证模式
const matchRequestSchema = z.object({
  category: z.nativeEnum(RequirementCategory, {
    required_error: "请选择服务分类",
  }),
  title: z.string().min(1, "请输入需求标题").max(100, "标题不能超过100个字符"),
  description: z
    .string()
    .min(10, "请详细描述您的需求，至少10个字符")
    .max(1000, "描述不能超过1000个字符"),
  grade: z.string().min(1, "请选择年级"),
  subjects: z.array(z.string()).min(1, "请至少选择一个科目"),
  location: z.string().min(1, "请选择地区"),
  budget: z.number().min(0, "预算不能为负数").optional(),
  urgency: z.number().min(1).max(10),
  expiredAt: z.date().optional(),
  mode: z.enum(["SMART_MATCH", "PUBLIC_POOL"]),
  additionalInfo: z.string().optional(),
});

type MatchRequestFormData = z.infer<typeof matchRequestSchema>;

// 匹配请求类型
const MatchRequestType = {
  STUDENT: "STUDENT_TO_PLANNER", // 学生找规划师
  PARENT: "PARENT_TO_PLANNER", // 家长找规划师
  PLANNER: "PLANNER_TO_PLANNER", // 规划师找规划师（转介）
  COMMUNITY_USER: "PLANNER_TO_PLANNER", // 机构批量匹配
  TENANT_ADMIN: "INSTITUTION_BATCH", // 机构批量匹配
};
// 年级选项
const GRADE_OPTIONS = [
  { value: "primary-1", label: "小学一年级" },
  { value: "primary-2", label: "小学二年级" },
  { value: "primary-3", label: "小学三年级" },
  { value: "primary-4", label: "小学四年级" },
  { value: "primary-5", label: "小学五年级" },
  { value: "primary-6", label: "小学六年级" },
  { value: "junior-1", label: "初中一年级" },
  { value: "junior-2", label: "初中二年级" },
  { value: "junior-3", label: "初中三年级" },
  { value: "senior-1", label: "高中一年级" },
  { value: "senior-2", label: "高中二年级" },
  { value: "senior-3", label: "高中三年级" },
  { value: "university", label: "大学" },
  { value: "adult", label: "成人" },
];

// 科目选项
const SUBJECT_OPTIONS = [
  { value: "chinese", label: "语文" },
  { value: "math", label: "数学" },
  { value: "english", label: "英语" },
  { value: "physics", label: "物理" },
  { value: "chemistry", label: "化学" },
  { value: "biology", label: "生物" },
  { value: "history", label: "历史" },
  { value: "geography", label: "地理" },
  { value: "politics", label: "政治" },
  { value: "programming", label: "编程" },
  { value: "art", label: "美术" },
  { value: "music", label: "音乐" },
  { value: "sports", label: "体育" },
];

// 地区选项 - 已迁移到 match-constants.ts

interface MatchRequestFormProps {
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  defaultValues?: Partial<MatchRequestFormData>;
  className?: string;
}

export function MatchRequestForm({
  onSuccess,
  onCancel,
  defaultValues,
  className,
}: MatchRequestFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, currentTenant } = useClientUser();

  const form = useForm<MatchRequestFormData>({
    resolver: zodResolver(matchRequestSchema),
    defaultValues: {
      urgency: 5,
      mode: "SMART_MATCH",
      subjects: [],
      ...defaultValues,
    },
  });

  const { watch, setValue } = form;
  const watchedCategory = watch("category");
  const watchedSubjects = watch("subjects");
  const watchedUrgency = watch("urgency");
  const watchedMode = watch("mode");

  const handleSubjectToggle = (subject: string) => {
    const current = watchedSubjects || [];
    const updated = current.includes(subject)
      ? current.filter((s) => s !== subject)
      : [...current, subject];
    setValue("subjects", updated);
  };

  const onSubmit = async (data: MatchRequestFormData) => {
    setLoading(true);
    setError(null);

    const requesterType = (MatchRequestType as unknown as any)?.[
      (currentTenant as any)?.role
    ];

    try {
      // 构建请求数据
      const requestData = {
        reuesterId: user?.id,
        tenantId: (currentTenant as any)?.tenant?.id,
        requesterType: requesterType || "STUDENT_TO_PLANNER", // 默认为学生类型
        requirements: {
          title: data.title,
          description: data.description,
          grade: data.grade,
          subjects: data.subjects,
          location: data.location,
          budget: data.budget,
          additionalInfo: data.additionalInfo,
        },
        category: data.category,
        urgency: data.urgency,
        mode: data.mode,
        expiredAt: data.expiredAt?.toISOString(),
      };

      const result = await apiClient.request("/api/match/requests", {
        method: "POST",
        body: JSON.stringify(requestData),
      });

      if (result.success) {
        onSuccess?.(result.data);
      } else {
        setError(result.message || "创建匹配请求失败");
      }
    } catch (err) {
      setError("网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const urgencyLabels = {
    1: "不急",
    2: "较缓",
    3: "一般",
    4: "较急",
    5: "普通",
    6: "重要",
    7: "紧急",
    8: "很急",
    9: "非常急",
    10: "极急",
  };

  return (
    <Card className={cn("w-full max-w-4xl mx-auto", className)}>
      <CardHeader>
        <CardTitle>创建匹配请求</CardTitle>
        <CardDescription>
          填写您的需求信息，我们将为您匹配最合适的规划师
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 基本信息 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">基本信息</h3>

              {/* 服务分类 */}
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>服务分类 *</FormLabel>
                    <FormControl>
                      <RequirementCategorySelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="请选择服务分类"
                      />
                    </FormControl>
                    <FormDescription>
                      选择最符合您需求的服务分类
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 需求标题 */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>需求标题 *</FormLabel>
                    <FormControl>
                      <Input placeholder="简要描述您的需求" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 详细描述 */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>详细描述 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请详细描述您的需求、目标和期望..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      详细的描述有助于为您匹配更合适的规划师
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 学习信息 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">学习信息</h3>

              {/* 年级 */}
              <FormField
                control={form.control}
                name="grade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>年级 *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择年级" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {GRADE_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 科目选择 */}
              <FormField
                control={form.control}
                name="subjects"
                render={() => (
                  <FormItem>
                    <FormLabel>相关科目 *</FormLabel>
                    <FormDescription>选择与您需求相关的科目</FormDescription>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {SUBJECT_OPTIONS.map((subject) => (
                        <Button
                          key={subject.value}
                          type="button"
                          variant={
                            watchedSubjects?.includes(subject.value)
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => handleSubjectToggle(subject.value)}
                          className="justify-start"
                        >
                          {subject.label}
                        </Button>
                      ))}
                    </div>
                    {watchedSubjects?.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {watchedSubjects.map((subject) => (
                          <Badge
                            key={subject}
                            variant="secondary"
                            className="gap-1"
                          >
                            {
                              SUBJECT_OPTIONS.find((s) => s.value === subject)
                                ?.label
                            }
                            <X
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => handleSubjectToggle(subject)}
                            />
                          </Badge>
                        ))}
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 服务要求 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">服务要求</h3>

              {/* 地区 */}
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>服务地区 *</FormLabel>
                    <FormControl>
                      <LocationSelector
                        value={field.value as Location}
                        onValueChange={field.onChange}
                        placeholder="请选择地区"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 预算 */}
              <FormField
                control={form.control}
                name="budget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>预算（元）</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="请输入预算范围"
                        {...field}
                        onChange={(e) =>
                          field.onChange(
                            e.target.value ? Number(e.target.value) : undefined,
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      可选填，有助于为您匹配合适价位的规划师
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 紧急程度 */}
              <FormField
                control={form.control}
                name="urgency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      紧急程度:{" "}
                      {
                        urgencyLabels[
                          watchedUrgency as keyof typeof urgencyLabels
                        ]
                      }
                    </FormLabel>
                    <FormControl>
                      <Slider
                        min={1}
                        max={10}
                        step={1}
                        value={[field.value]}
                        onValueChange={(value) => field.onChange(value[0])}
                        className="w-full"
                      />
                    </FormControl>
                    <FormDescription>
                      紧急程度影响匹配优先级，数值越高优先级越高
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 匹配模式 */}
              <FormField
                control={form.control}
                name="mode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>匹配模式</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="SMART_MATCH">智能匹配</SelectItem>
                        <SelectItem value="PUBLIC_POOL">公开抢单</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {watchedMode === "SMART_MATCH"
                        ? "系统自动为您推荐最合适的规划师"
                        : "发布到公开池，让规划师主动响应"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 过期时间 */}
              <FormField
                control={form.control}
                name="expiredAt"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>过期时间</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: zhCN })
                            ) : (
                              <span>选择过期时间</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>可选，设置请求的有效期</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 补充信息 */}
            <FormField
              control={form.control}
              name="additionalInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>补充信息</FormLabel>
                  <FormControl>
                    <Textarea placeholder="其他需要说明的信息..." {...field} />
                  </FormControl>
                  <FormDescription>可填写其他特殊要求或说明</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 表单操作 */}
            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={loading} className="flex-1">
                {loading ? "创建中..." : "创建匹配请求"}
              </Button>
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  取消
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
