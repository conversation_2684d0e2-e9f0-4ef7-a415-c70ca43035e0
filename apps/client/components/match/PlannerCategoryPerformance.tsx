import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar,
} from "recharts";
import {
  TrendingUp,
  TrendingDown,
  Star,
  CheckCircle,
  AlertCircle,
  Users,
  BarChart3,
  Award,
} from "lucide-react";

interface PlannerPerformance {
  category: string;
  totalRequests: number;
  confirmedRequests: number;
  successRate: number;
  avgRating: number;
  evaluationCount: number;
}

interface PlannerCategoryPerformanceProps {
  plannerId: string;
  className?: string;
}

const CATEGORY_COLORS = {
  ACADEMIC_PLANNING: "#3b82f6",
  CAREER_GUIDANCE: "#10b981",
  STUDY_ABROAD: "#f59e0b",
  SKILL_DEVELOPMENT: "#8b5cf6",
  COMPETITION_TRAINING: "#ef4444",
  PSYCHOLOGICAL_SUPPORT: "#06b6d4",
  SPECIAL_NEEDS: "#84cc16",
  OTHER: "#6b7280",
};

const CATEGORY_NAMES = {
  ACADEMIC_PLANNING: "学业规划",
  CAREER_GUIDANCE: "职业指导",
  STUDY_ABROAD: "留学咨询",
  SKILL_DEVELOPMENT: "技能发展",
  COMPETITION_TRAINING: "竞赛培训",
  PSYCHOLOGICAL_SUPPORT: "心理支持",
  SPECIAL_NEEDS: "特殊需求",
  OTHER: "其他",
};

export default function PlannerCategoryPerformance({
  plannerId,
  className = "",
}: PlannerCategoryPerformanceProps) {
  const [performance, setPerformance] = useState<PlannerPerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPerformance = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(
          `/api/match/planner/${plannerId}/performance`,
        );
        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || "获取数据失败");
        }

        setPerformance(data.data);
      } catch (err) {
        console.error("获取规划师分类表现失败:", err);
        setError(err instanceof Error ? err.message : "获取数据失败");
      } finally {
        setLoading(false);
      }
    };

    if (plannerId) {
      fetchPerformance();
    }
  }, [plannerId]);

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-72" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <Skeleton key={i} className="h-32" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (performance.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>暂无分类表现数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 计算总体统计
  const totalRequests = performance.reduce(
    (sum, p) => sum + p.totalRequests,
    0,
  );
  const totalConfirmed = performance.reduce(
    (sum, p) => sum + p.confirmedRequests,
    0,
  );
  const overallSuccessRate =
    totalRequests > 0 ? totalConfirmed / totalRequests : 0;
  const totalEvaluations = performance.reduce(
    (sum, p) => sum + p.evaluationCount,
    0,
  );
  const weightedRating =
    performance.reduce((sum, p) => sum + p.avgRating * p.evaluationCount, 0) /
      totalEvaluations || 0;

  // 准备图表数据
  const barChartData = performance.map((p) => ({
    category:
      CATEGORY_NAMES[p.category as keyof typeof CATEGORY_NAMES] || p.category,
    requests: p.totalRequests,
    confirmed: p.confirmedRequests,
    successRate: p.successRate * 100,
    rating: p.avgRating,
  }));

  const pieChartData = performance.map((p) => ({
    name:
      CATEGORY_NAMES[p.category as keyof typeof CATEGORY_NAMES] || p.category,
    value: p.totalRequests,
    color:
      CATEGORY_COLORS[p.category as keyof typeof CATEGORY_COLORS] ||
      CATEGORY_COLORS.OTHER,
  }));

  const radialData = performance.map((p) => ({
    category:
      CATEGORY_NAMES[p.category as keyof typeof CATEGORY_NAMES] || p.category,
    successRate: p.successRate * 100,
    fill:
      CATEGORY_COLORS[p.category as keyof typeof CATEGORY_COLORS] ||
      CATEGORY_COLORS.OTHER,
  }));

  const getPerformanceLevel = (successRate: number) => {
    if (successRate >= 0.9)
      return { level: "优秀", color: "bg-green-500", icon: TrendingUp };
    if (successRate >= 0.7)
      return { level: "良好", color: "bg-blue-500", icon: TrendingUp };
    if (successRate >= 0.5)
      return { level: "一般", color: "bg-yellow-500", icon: TrendingDown };
    return { level: "待提升", color: "bg-red-500", icon: TrendingDown };
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-blue-600";
    if (rating >= 3.5) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 总体概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            分类表现概览
          </CardTitle>
          <CardDescription>规划师在各服务分类下的详细表现数据</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {totalRequests}
              </div>
              <div className="text-sm text-muted-foreground">总请求数</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {totalConfirmed}
              </div>
              <div className="text-sm text-muted-foreground">成功匹配</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {(overallSuccessRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">总体成功率</div>
            </div>
            <div className="text-center">
              <div
                className={`text-2xl font-bold ${getRatingColor(weightedRating)}`}
              >
                {weightedRating.toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">平均评分</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 详细表现数据 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {performance.map((perf) => {
          const performanceLevel = getPerformanceLevel(perf.successRate);
          const Icon = performanceLevel.icon;

          return (
            <Card
              key={perf.category}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {CATEGORY_NAMES[
                      perf.category as keyof typeof CATEGORY_NAMES
                    ] || perf.category}
                  </CardTitle>
                  <Badge
                    variant="secondary"
                    className={`${performanceLevel.color} text-white`}
                  >
                    {performanceLevel.level}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">请求数:</span>
                    <span className="font-medium">{perf.totalRequests}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-muted-foreground">成功:</span>
                    <span className="font-medium text-green-600">
                      {perf.confirmedRequests}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">成功率</span>
                    <span className="font-medium flex items-center gap-1">
                      <Icon className="h-3 w-3" />
                      {(perf.successRate * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${performanceLevel.color}`}
                      style={{ width: `${perf.successRate * 100}%` }}
                    />
                  </div>
                </div>

                {perf.evaluationCount > 0 && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">平均评分</span>
                    <div className="flex items-center gap-1">
                      <Star
                        className={`h-4 w-4 ${getRatingColor(perf.avgRating)}`}
                      />
                      <span
                        className={`font-medium ${getRatingColor(perf.avgRating)}`}
                      >
                        {perf.avgRating.toFixed(1)}
                      </span>
                      <span className="text-muted-foreground">
                        ({perf.evaluationCount}评价)
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 图表展示 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 请求数量对比 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">分类请求量对比</CardTitle>
            <CardDescription>各服务分类的请求数量分布</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={barChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="category"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="requests" fill="#3b82f6" name="总请求" />
                <Bar dataKey="confirmed" fill="#10b981" name="成功匹配" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 成功率对比 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">分类成功率对比</CardTitle>
            <CardDescription>各服务分类的匹配成功率</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RadialBarChart data={radialData}>
                <RadialBar
                  dataKey="successRate"
                  cornerRadius={10}
                  fill={(entry) => entry.fill}
                />
                <Tooltip formatter={(value) => [`${value}%`, "成功率"]} />
              </RadialBarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 请求分布饼图 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">请求分布</CardTitle>
          <CardDescription>各服务分类的请求数量占比</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={pieChartData}
                cx="50%"
                cy="50%"
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) =>
                  `${name} ${(percent * 100).toFixed(0)}%`
                }
              >
                {pieChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}
