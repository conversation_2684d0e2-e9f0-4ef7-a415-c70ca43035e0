"use client";

import { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";
import { Label } from "@workspace/ui/components/label";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { useClientUser, TenantInfo } from "@/contexts/UserContext";

interface TenantSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tenants: TenantInfo[];
}

export function TenantSelectionDialog({
  open,
  onOpenChange,
  tenants,
}: TenantSelectionDialogProps) {
  const [selectedTenant, setSelectedTenant] = useState(tenants[0]?.id || "");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { currentTenant, switchTenant } = useClientUser();

  useEffect(() => {
    setSelectedTenant(currentTenant?.id || "");
  }, [currentTenant]);

  const handleTenantSelect = async () => {
    if (!selectedTenant) return;

    setIsLoading(true);
    setError("");

    try {
      const result = await switchTenant(selectedTenant);

      if (result) {
        // 切换成功，关闭弹窗并跳转
        onOpenChange(false);
      } else {
        setError("切换租户失败，请重试");
      }
    } catch (error) {
      console.error("切换租户失败:", error);
      setError("网络连接失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setError("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>选择工作空间</DialogTitle>
          <DialogDescription>
            您属于多个工作空间，请选择要访问业务系统的工作空间
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 错误信息 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 租户选择 */}
          <div className="space-y-3">
            <RadioGroup
              value={selectedTenant}
              onValueChange={setSelectedTenant}
            >
              {tenants.map((tenant) => (
                <div key={tenant.id} className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem
                      value={tenant.id}
                      id={tenant.id}
                      disabled={isLoading}
                    />
                    <Label
                      htmlFor={tenant.id}
                      className="flex-1 cursor-pointer p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                    >
                      <div className="font-medium text-sm">{tenant.name}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {tenant.type} · {tenant.role}
                      </div>
                    </Label>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handleTenantSelect}
              disabled={isLoading || !selectedTenant}
              className="flex-1"
            >
              {isLoading ? "切换中..." : "进入工作空间"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
