import { PrismaClient } from "../src/generated/prisma";
import { createHash } from "crypto";

const prisma = new PrismaClient();

// MD5加密函数
function hashPassword(password: string): string {
  return createHash("md5").update(password).digest("hex");
}

async function main() {
  console.log("开始种子数据...");

  // ==================== 管理后台用户 ====================

  // 创建测试管理员用户
  const adminUser = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "admin",
      name: "超级管理员",
      password: hashPassword("123456"), // 密码: 123456
      role: "SUPER_ADMIN",
      status: "ACTIVE",
      permissions: ["*"], // 所有权限
      allowedModules: ["*"], // 所有模块
    },
  });

  console.log("创建管理员用户:", adminUser);

  // 创建测试普通管理员
  const normalAdmin = await prisma.adminUser.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "manager",
      name: "系统管理员",
      password: hashPassword("123456"), // 密码: 123456
      role: "ADMIN",
      status: "ACTIVE",
      permissions: [
        "users:read",
        "users:write",
        "courses:read",
        "courses:write",
        "system:read",
      ],
      allowedModules: ["users", "courses", "system"],
    },
  });

  console.log("创建普通管理员:", normalAdmin);

  // ==================== 客户端体验用户 ====================

  // 创建体验用户
  const demoUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "demo",
      name: "体验用户",
      password: hashPassword("123456"), // 密码: 123456
      isActive: true,
      emailVerified: true,
      bio: "这是一个演示账户，用于体验系统功能",
    },
  });

  console.log("创建体验用户:", demoUser);

  // 创建机构管理员用户
  const institutionAdmin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "institution_admin",
      name: "机构管理员",
      password: hashPassword("123456"), // 密码: 123456
      isActive: true,
      emailVerified: true,
      bio: "演示机构管理员账户",
    },
  });

  console.log("创建机构管理员:", institutionAdmin);

  // 创建规划师用户
  const plannerUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      username: "planner",
      name: "资深规划师",
      password: hashPassword("123456"), // 密码: 123456
      isActive: true,
      emailVerified: true,
      bio: "专业升学规划师，5年经验",
    },
  });

  console.log("创建规划师用户:", plannerUser);

  // ==================== 创建租户/工作区 ====================

  // 创建机构租户
  const institutionTenant = await prisma.tenant.upsert({
    where: {
      customDomain: "demo-institution",
    },
    update: {},
    create: {
      name: "演示教育机构",
      type: "INSTITUTION",
      status: "ACTIVE",
      description: "这是一个演示教育机构，用于展示系统功能",
      contactName: "机构负责人",
      contactPhone: "13800138000",
      contactEmail: "<EMAIL>",
      address: "北京市朝阳区示例街道123号",
      customDomain: "demo-institution",
      settings: {
        allowStudentSelfRegister: true,
        requireParentApproval: false,
        defaultPlanType: "升学规划",
      },
    },
  });

  console.log("创建机构租户:", institutionTenant);

  // 创建个人规划师租户
  const plannerTenant = await prisma.tenant.upsert({
    where: {
      customDomain: "planner-li",
    },
    update: {},
    create: {
      name: "李老师工作室",
      type: "INDIVIDUAL",
      status: "ACTIVE",
      description: "专业升学规划服务",
      contactName: "李老师",
      contactPhone: "13900139000",
      contactEmail: "<EMAIL>",
      address: "上海市浦东新区规划路456号",
      customDomain: "planner-li",
      settings: {
        maxStudents: 50,
        specialties: ["国际升学", "艺术留学"],
        workingHours: "周一至周五 9:00-18:00",
      },
    },
  });

  console.log("创建个人规划师租户:", plannerTenant);

  // ==================== 创建租户用户关系 ====================

  // 体验用户 -> 机构租户 (多个角色)
  const demoTenantUser = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: demoUser.id,
        tenantId: institutionTenant.id,
      },
    },
    update: {},
    create: {
      userId: demoUser.id,
      tenantId: institutionTenant.id,
      role: "TENANT_ADMIN",
      isActive: true,
    },
  });

  console.log("创建体验用户租户关系:", demoTenantUser);

  // 机构管理员 -> 机构租户
  const institutionTenantUser = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: institutionAdmin.id,
        tenantId: institutionTenant.id,
      },
    },
    update: {},
    create: {
      userId: institutionAdmin.id,
      tenantId: institutionTenant.id,
      role: "TENANT_ADMIN",
      isActive: true,
    },
  });

  console.log("创建机构管理员租户关系:", institutionTenantUser);

  // 规划师 -> 个人租户
  const plannerTenantUser = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: plannerUser.id,
        tenantId: plannerTenant.id,
      },
    },
    update: {},
    create: {
      userId: plannerUser.id,
      tenantId: plannerTenant.id,
      role: "TENANT_ADMIN",
      isActive: true,
    },
  });

  console.log("创建规划师租户关系:", plannerTenantUser);

  // 规划师也加入机构（展示多租户功能）
  const plannerInInstitution = await prisma.tenantUser.upsert({
    where: {
      userId_tenantId: {
        userId: plannerUser.id,
        tenantId: institutionTenant.id,
      },
    },
    update: {},
    create: {
      userId: plannerUser.id,
      tenantId: institutionTenant.id,
      role: "PLANNER",
      isActive: true,
    },
  });

  console.log("创建规划师在机构的关系:", plannerInInstitution);

  // ==================== 创建规划师档案 ====================

  const plannerProfile = await prisma.planner.upsert({
    where: { tenantUserId: plannerTenantUser.id },
    update: {},
    create: {
      tenantUserId: plannerTenantUser.id,
      tenantId: plannerTenant.id,
      title: "高级升学规划师",
      specialties: ["国际教育", "艺术留学", "理工科升学"],
      experience: 5,
      introduction:
        "拥有5年升学规划经验，成功帮助200+学生进入理想院校。擅长国际教育路径规划和艺术类专业申请指导。",
      totalStudents: 156,
      successRate: 0.92,
      rating: 4.8,
    },
  });

  console.log("创建规划师档案:", plannerProfile);

  // ==================== 创建演示学生 ====================

  const demoStudent = await prisma.student.create({
    data: {
      tenantId: institutionTenant.id,
      name: "张小明",
      gender: "男",
      birthday: new Date("2008-05-15"),
      phone: "13700137000",
      email: "<EMAIL>",
      school: "北京市第一中学",
      grade: "高二",
      gpa: 3.8,
      rank: 15,
      parentName: "张父",
      parentPhone: "13600136000",
      parentEmail: "<EMAIL>",
      familyBackground: "父母均为高校教师，注重教育，希望孩子能进入985高校",
      tags: ["理科强", "数学竞赛", "编程爱好"],
      notes: "学习成绩优秀，对计算机科学感兴趣，有参加数学竞赛的经历",
      status: "ACTIVE",
    },
  });

  console.log("创建演示学生:", demoStudent);

  // ==================== 创建订阅计划 ====================

  // 免费版
  const freePlan = await prisma.subscriptionPlan.upsert({
    where: { code: "free" },
    update: {},
    create: {
      name: "免费版",
      code: "free",
      type: "FREE",
      description: "适合个人用户体验基础功能",
      features: {
        studentCRM: true,
        basicReports: true,
        communityAccess: true,
      },
      limits: {
        maxUsers: 1,
        maxStudents: 5,
        maxStorage: 100,
      },
      maxUsers: 1,
      maxStudents: 5,
      maxPlanners: 1,
      maxStorage: 100,
      maxAIRequests: 10,
      hasStudentCRM: true,
      hasAITools: false,
      hasDataDashboard: false,
      hasScheduledTasks: false,
      hasLandingPage: false,
      hasCommunity: true,
      hasCustomBranding: false,
      hasAPIAccess: false,
      hasPrioritySupport: false,
      displayOrder: 1,
      isPopular: false,
      isActive: true,
      isVisible: true,
    },
  });

  // 专业版
  const proPlan = await prisma.subscriptionPlan.upsert({
    where: { code: "professional" },
    update: {},
    create: {
      name: "专业版",
      code: "professional",
      type: "PERSONAL",
      description: "适合专业规划师和小型机构",
      features: {
        studentCRM: true,
        aiTools: true,
        advancedReports: true,
        landingPage: true,
        scheduling: true,
      },
      limits: {
        maxUsers: 3,
        maxStudents: 50,
        maxStorage: 1000,
      },
      maxUsers: 3,
      maxStudents: 50,
      maxPlanners: 2,
      maxStorage: 1000,
      maxAIRequests: 500,
      hasStudentCRM: true,
      hasAITools: true,
      hasDataDashboard: true,
      hasScheduledTasks: true,
      hasLandingPage: true,
      hasCommunity: true,
      hasCustomBranding: false,
      hasAPIAccess: false,
      hasPrioritySupport: false,
      displayOrder: 2,
      isPopular: true,
      badge: "最受欢迎",
      isActive: true,
      isVisible: true,
    },
  });

  console.log("创建订阅计划:", { freePlan, proPlan });

  // ==================== 为租户创建订阅 ====================

  // 机构租户使用专业版
  const institutionSubscription = await prisma.subscription.upsert({
    where: { tenantId: institutionTenant.id },
    update: {},
    create: {
      tenantId: institutionTenant.id,
      planId: proPlan.id,
      status: "TRIAL",
      billingCycle: "MONTHLY",
      currentPrice: 299.0,
      trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天试用
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年
    },
  });

  // 个人规划师使用免费版
  const plannerSubscription = await prisma.subscription.upsert({
    where: { tenantId: plannerTenant.id },
    update: {},
    create: {
      tenantId: plannerTenant.id,
      planId: freePlan.id,
      status: "ACTIVE",
      billingCycle: "MONTHLY",
      currentPrice: 0.0,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年
    },
  });

  console.log("创建订阅关系:", {
    institutionSubscription,
    plannerSubscription,
  });

  console.log("\n🎉 种子数据创建完成!");
  console.log("\n📋 测试账户信息:");
  console.log("==================== 管理后台 ====================");
  console.log("超级管理员: <EMAIL> / 123456");
  console.log("系统管理员: <EMAIL> / 123456");
  console.log("\n==================== 客户端体验账户 ====================");
  console.log("体验用户(多角色): <EMAIL> / 123456");
  console.log("机构管理员: <EMAIL> / 123456");
  console.log("规划师: <EMAIL> / 123456");
  console.log("\n🏢 租户信息:");
  console.log("- 演示教育机构 (机构类型)");
  console.log("- 李老师工作室 (个人规划师)");
  console.log("\n👥 学生信息:");
  console.log("- 张小明 (演示学生)");
}

main()
  .catch((e) => {
    console.error("种子数据失败:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
