import Redis from "ioredis";
import type { Redis as RedisClient, RedisOptions } from "ioredis";

// 导出 Redis 类型和构造函数
export { Redis };
export type { RedisClient, RedisOptions };

// Redis 配置选项
const redisConfig: RedisOptions = {
  host: process.env.REDIS_HOST || "localhost",
  port: parseInt(process.env.REDIS_PORT || "6379"),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || "1"),
  maxRetriesPerRequest: 3,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  reconnectOnError: (err) => {
    const targetError = "READONLY";
    if (err.message.includes(targetError)) {
      // 只在 READONLY 错误时重连
      return true;
    }
    return false;
  },
};

// 创建默认 Redis 客户端实例
export const redis = new Redis(redisConfig);

// 错误处理
redis.on("error", (err) => {
  console.error("Redis Client Error:", err);
});

redis.on("connect", () => {
  console.log("Redis Client Connected");
});

redis.on("ready", () => {
  console.log("Redis Client Ready");
});

// 工厂函数：创建新的 Redis 实例
export function createRedisClient(options?: RedisOptions): RedisClient {
  return new Redis({
    ...redisConfig,
    ...options,
  });
}

// Redis 键命名空间助手
export const REDIS_KEYS = {
  // 用户会话
  SESSION: (token: string) => `session:${token}`,

  // 用户权限缓存
  USER_PERMISSIONS: (userId: string) => `permissions:user:${userId}`,

  // 租户用户权限缓存
  TENANT_USER_PERMISSIONS: (tenantUserId: string) =>
    `permissions:tenant_user:${tenantUserId}`,

  // 角色权限缓存
  ROLE_PERMISSIONS: (role: string) => `permissions:role:${role}`,

  // 权限检查缓存
  PERMISSION_CHECK: (userId: string, permission: string) =>
    `perm_check:${userId}:${permission}`,

  // 通用缓存键
  CACHE: (key: string) => `cache:${key}`,
} as const;

// 缓存过期时间（秒）
export const CACHE_TTL = {
  SESSION: 7 * 24 * 60 * 60, // 7天
  PERMISSIONS: 60 * 60, // 1小时
  PERMISSION_CHECK: 5 * 60, // 5分钟
  SHORT: 60, // 1分钟
  MEDIUM: 5 * 60, // 5分钟
  LONG: 60 * 60, // 1小时
  DAY: 24 * 60 * 60, // 1天
} as const;

// 导出一些常用的 Redis 操作助手函数
export const redisHelpers = {
  // 设置带过期时间的值
  async setWithExpiry(key: string, value: any, ttl: number): Promise<void> {
    await redis.setex(key, ttl, JSON.stringify(value));
  },

  // 获取并解析 JSON 值
  async getJSON<T>(key: string): Promise<T | null> {
    const value = await redis.get(key);
    if (!value) return null;
    try {
      return JSON.parse(value) as T;
    } catch {
      return null;
    }
  },

  // 删除匹配模式的所有键
  async deletePattern(pattern: string): Promise<number> {
    const keys = await redis.keys(pattern);
    if (keys.length === 0) return 0;
    return await redis.del(...keys);
  },

  // 使用 pipeline 批量操作
  async batchGet(keys: string[]): Promise<(string | null)[]> {
    if (keys.length === 0) return [];
    const pipeline = redis.pipeline();
    keys.forEach((key) => pipeline.get(key));
    const results = await pipeline.exec();
    return results
      ? results.map(([err, value]) => (err ? null : (value as string | null)))
      : [];
  },
};
